/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-10-04
 */

export const CustomerTailwindConfig = {
    extend: {
        screens: {
            'max-height-720': { raw: '(max-height: 720px)' }, // Custom max-height query
            'max-390': { raw: '(max-width: 390px)' }, // Custom max-width query
            'max-320': { raw: '(max-width: 320px)' },
        },
        fontFamily: {
            onest: ['"Onest"', 'sans-serif'],
            orbitron: ['"Orbitron"', 'sans-monospace'],
        },
        colors: {
            primary: '#171717',
            secondary: {
                blue: '#5171c7',
                orange: '#f05640',
            },
            accent: {
                green: '#6ac79d',
                purple: '#9f6eef',
                pink: '#ee77b7',
            },
            feedback: {
                success: '#0bab68',
                error: '#c01a3a',
                warning: '#fe9143',
                info: '#4299ff',
                surface: {
                    success: '#c9ffe8',
                    error: '#ffefef',
                    warning: '#fde8bb',
                    info: '#deedff',
                },
            },
            neutral: {
                dark: '#747474',
                medium: '#a2a2a2',
                light: '#d1d1d1',
            },
            surface: {
                DEFAULT: '#dce3f4',
                lighter: '#f1f4fe',
                lightest: '#f5fbff',
                white: '#ffffff',
            },
            black: {
                900: '#000000',
                800: '#171717',
                700: '#454545',
                600: '#747474',
                500: '#a2a2a2',
                400: '#d1d1d1',
            },
            blue: {
                900: '#111729',
                800: '#212e50',
                700: '#314478',
                600: '#415aa0',
                500: '#5171c7',
                400: '#748dd2',
                300: '#97aadd',
                200: '#b9c6e9',
                100: '#dce3f4',
            },
            orange: {
                900: '#31120e',
                800: '#61231a',
                700: '#903427',
                600: '#c04533',
                500: '#f05640',
                400: '#f37866',
                300: '#f69a8c',
                200: '#f9bbb3',
                100: '#fcddd9',
            },
            purple: {
                900: '#1a0033',
                800: '#311b92',
                700: '#4527a0',
                600: '#673ab7',
                500: '#7e57c2',
                400: '#9575cd',
                300: '#b39ddb',
                200: '#d1c4e9',
                100: '#ede7f6',
            },
            pink: {
                900: '#301925',
                800: '#60304a',
                700: '#8f486e',
                600: '#bf6092',
                500: '#ee77b7',
                400: '#f192c5',
                300: '#f5aed4',
                200: '#f8c9e2',
                100: '#fce4f1',
            },
            green: {
                900: '#15281f',
                800: '#2a503f',
                700: '#40775e',
                600: '#559f7e',
                500: '#6ac79d',
                400: '#88d2b1',
                300: '#a6ddc4',
                200: '#c3e9d8',
                100: '#e1f4eb',
            },
            gradient: {
                elite: {
                    DEFAULT: '#b292e6',
                    badge: {
                        start: '#825bc1',
                        end: '#b899eb',
                    },
                },
                basic: {
                    DEFAULT: '#869bd6',
                    badge: {
                        start: '#415aa0',
                        end: '#97aadd',
                    },
                },
            },
        },
        backgroundColor: {
            'modal-overlay': 'rgba(0, 0, 0, 0.7)',
            card: {
                shadow: '#6e5791',
            },
            'scrollbar-thumb': '#c8d2e680',
        },
        borderRadius: {
            '4xl': '30px',
            '3xl': '20px',
        },
        boxShadow: {
            sm: '0px 2px 4px #0000000d',
            md: '0px 4px 6px #89b0bd2e',
            lg: '0px 6px 10px #e0e9fecc',
            'surround-sm': '0px 0px 4px #0000000d',
            'surround-md': '0px 0px 6px #89b0bd2e',
            'surround-lg': '0px 0px 10px #e0e9fecc',
            top: '0px 4px 10px #0000000d',
            'curve-after': '0 24px 0 0 #ffffff',
            'curve-before': '0 20px 0 0 #ffffff',
        },
        maxWidth: {
            '7xl': '1200px',
        },

        backgroundImage: {
            'gradient-to-top-black':
                'linear-gradient(to top, rgba(0,0,0,1), rgba(0,0,0,0.8), rgba(0,0,0,0), rgba(0,0,0,0), transparent)',
            'card-background': `
      radial-gradient(circle at 80% 20%, rgba(255,255,255,0.3) 0%, transparent 50%),
      radial-gradient(circle at 65% 0%, rgba(160,117,255,0.35) 0%, transparent 60%),
      linear-gradient(135deg, #0e0e12 0%, #1b0f29 100%)
    `,
        },

        keyframes: {
            fadeIn: {
                '0%': { opacity: '0', transform: 'scale(0.8)' },
                '100%': { opacity: '1', transform: 'scale(1)' },
            },
            fadeOut: {
                '0%': { opacity: '1', transform: 'scale(1)' },
                '100%': { opacity: '0', transform: 'scale(0.8)' },
            },
        },
        animation: {
            fadeIn: 'fadeIn 0.3s ease-in-out',
            fadeOut: 'fadeOut 0.2s ease-in-out forwards',
        },
    },
};

