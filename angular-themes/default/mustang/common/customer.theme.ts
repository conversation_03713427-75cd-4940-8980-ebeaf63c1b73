/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-09-18
 */

export interface ThemeObject {
    [key: string]: any;
}

// Global theme
export const GlobalTheme: { [key: string]: ThemeObject } = {

    // w-full max-w-[23rem] py-3 px-4 bg-primary text-white font-medium rounded-4xl disabled:bg-surface

    btn_primary: {
        font_weight: 'font-medium',
        text_align: 'text-center',
        font_color: 'text-white',
        bg_color: 'bg-primary',
        border_radius: 'rounded-lg',
        hover: { bg_color: 'bg-primary-800' },
        focus: {
            ring_size: 'ring-4',
            ring_color: 'ring-primary-300',
            outline: 'outline-none',
        },
    },

    // btn_primary: {
    // bg_color: 'bg-gray-600'
    // }
};









