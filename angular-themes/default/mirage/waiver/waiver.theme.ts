/**
 * @fileoverview
 * Site level theme configuration for Waiver application
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-09-18
 */

export interface ThemeObject {
    [key: string]: any;
}

export const SiteWaiverTheme: ThemeObject = {

};

export const HeaderTheme: ThemeObject = {

};

export const FooterTheme: ThemeObject = {

};

export const LandingPageTheme: ThemeObject = {

};

export const AboutPageTheme: ThemeObject = {

};

export const RegisterFormTheme: ThemeObject = {

};

export const AddMinorFormTheme: ThemeObject = {

};