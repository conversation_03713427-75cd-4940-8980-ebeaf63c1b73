/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-02
 */

import { TransferState, inject, makeStateKey } from '@angular/core';

const ENV_VARS_KEY = makeStateKey<{ [key: string]: string }>('envVars');

// export function transferStateFactory(transferState: TransferState) {
//     return () => {
//         const envVars = {
//             PARAFAIT_API_BASEURL: process.env['PARAFAIT_API_BASEURL'] || '',
//             APP_NODEAPI_BASEURL: process.env['APP_NODEAPI_BASEURL'] || '',
//             TEST: process.env['TEST'] || '',
//             // Add other environment variables as needed
//         };
//         transferState.set(ENV_VARS_KEY, envVars);
//     };
// }


export function transferEnvVars(): void {
    const transferState = inject(TransferState);
    const envVars: Record<string, string> = {
        PARAFAIT_API_BASEURL: process.env['PARAFAIT_API_BASEURL'] || '',
        APP_NODEAPI_BASEURL: process.env['APP_NODEAPI_BASEURL'] || '',
        API_JSON_BASEURL: process.env['API_JSON_BASEURL'] || '',
    };
    transferState.set(ENV_VARS_KEY, envVars); // Ensure correct type
}

