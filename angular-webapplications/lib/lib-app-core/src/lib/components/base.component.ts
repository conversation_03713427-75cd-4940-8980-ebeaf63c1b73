/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-09-18
 */
import { Directive, OnInit, OnDestroy, Injector, inject } from '@angular/core';
import { ThemeService } from '../services/theme.service';

// @Directive is used since this is a base class for components to extend, and not a component itself
@Directive() 
export abstract class BaseComponent {
	// 'menu_items' holds the common menu items that will be used across multiple components.
	protected menu_items: any;

	// Abstract method for derived components to define component-specific theme
	protected abstract getRelevantTheme(): any;
	
	// Abstract method for derived components to define site-wide theme
	protected abstract getSiteTheme(): any;

    themeService = inject(ThemeService)

	// The constructor injects the ThemeService to manage styles/themes across the application.
	constructor(
		
	) {
		// Initialize a common set of menu items for navigation. These are available in all components that extend this base class.
		this.menu_items = [
			{
				id: 1,
				title: 'Home', // The title of the menu item
				url: '/', // The URL to navigate to when this menu item is clicked
				icon: 'home', // Icon for this menu item (e.g., home icon)
				children: [] // Can be populated with child menu items if needed
			},
			{
				id: 3,
				title: 'About Us',
				url: '/ui-components',
				icon: 'info',
				children: []
			},
			{
				id: 4,
				title: 'Contact',
				url: '/contact',
				icon: 'contact',
				children: []
			}
		];

		// Sets the site-wide theme based on the derived component's implementation of getSiteTheme()
		this.themeService.setSiteTheme(this.getSiteTheme());
	}

	/**
	 * Generates a list of Tailwind CSS classes for a button.
	 * @param btnStyle - The type of button (e.g., 'primary', 'accent', etc.)
	 * @param options - Additional options for customizing the button's appearance
	 * @returns string - Tailwind classes for button styling
	 */
    getButtonClass(btnStyle: string, options: any): string {
        // Uses the themeService to get button classes based on the component's theme and style.
        return this.themeService.getButtonClasses(this.getRelevantTheme(), btnStyle, options);
    }
	
	/**
	 * Helper function to return the Tailwind CSS classes for a primary button.
	 * @param options - Additional options for the button (e.g., size, color, etc.)
	 * @returns string - Tailwind classes for the primary button
	 */
    getBtnPrimary(options?: any): string {
        return this.getButtonClass('primary', options);
    }

	/**
	 * Helper function to return the Tailwind CSS classes for a stroked primary button (outlined button).
	 * @param options - Additional options for the button (e.g., size, color, etc.)
	 * @returns string - Tailwind classes for a stroked primary button
	 */
	getBtnPrimaryStroked(options?: any): string {
        return this.getButtonClass('primary_stroked', options);
    }

	/**
	 * Helper function to return the Tailwind CSS classes for an accent button.
	 * @param options - Additional options for the button (e.g., size, color, etc.)
	 * @returns string - Tailwind classes for an accent button
	 */
    getBtnAccent(options?: any): string {
        return this.getButtonClass('accent', options);
    }

	/**
	 * Helper function to return the Tailwind CSS classes for a stroked accent button (outlined button).
	 * @param options - Additional options for the button (e.g., size, color, etc.)
	 * @returns string - Tailwind classes for a stroked accent button
	 */
	getBtnAccentStroked(options?: any): string {
        return this.getButtonClass('primary_stroked', options); // This might be a typo; should it be 'accent_stroked'?
    }

	/**
	 * Fetches the input field CSS classes based on the theme and element's ID.
	 * @param elementId - Optional ID of the input element to get specific styles.
	 * @returns string - Tailwind classes for the input field
	 */
	getInputClass(elementId?: string): any {
		// Retrieves input-specific styles from the theme service.
		return this.themeService.getInputClass(this.getRelevantTheme(), elementId);
	}

	/**
	 * Fetches header styling based on the current theme and section name.
	 * @param section - The section of the page (e.g., 'header', 'subheader', etc.)
	 * @returns string - Tailwind classes for the section header
	 */
	getHeaderClass(section: string) {
		// Retrieves header-specific styles from the theme service.
		return this.themeService.getHeaderClass(this.getRelevantTheme(), section);
	}
}
