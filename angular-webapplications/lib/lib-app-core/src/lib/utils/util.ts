/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-09-18
 */

import { effect, EnvironmentInjector, inject, Injector, runInInjectionContext, Signal, signal } from "@angular/core";
import { APP_CONSTANTS_TOKEN, EnvService } from "lib-app-core";

// export function getEnvValue(obj: any, path: string): any {
//     return path.split('.').reduce((acc, part) => acc && acc[part], obj);
// }


/**
 * Converts a nested object into a Tailwind CSS class list string.
 * 
 * @param obj The object to convert.
 * @param parentKey Optional parent key for handling nested properties.
 * @returns A string with concatenated Tailwind CSS classes.
 */
export function convertToTailwindClassList(obj: any): string {
    let classes: string[] = [];
    let stack: Array<{ currentObj: any, parentKey: string }> = [{ currentObj: obj, parentKey: '' }];

    while (stack.length > 0) {
        const { currentObj, parentKey } = stack.pop()!;

        for (const key in currentObj) {
            if (currentObj.hasOwnProperty(key)) {
                const value = currentObj[key];
                const currentKey = parentKey ? `${parentKey}:${key}` : key;

                if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                    // Push nested object onto the stack
                    stack.push({ currentObj: value, parentKey: currentKey });
                } else {
                    // Base case: concatenate class name with its value
                    if (parentKey) {
                        classes.push(`${parentKey}:${value}`);
                    } else {
                        classes.push(value);
                    }
                }
            }
        }
    }

    return classes.join(' ');
}


/**
 * Retrieves the template URL from the environment object.
 * 
 * @param env - An object that may contain a `theme` property (the template URL).
 * @param default_template - The default template file name to use if the `theme` is not provided.
 * @returns A string representing the template URL. If `theme` is defined and not empty, returns the `theme`.
 *          Otherwise, it returns a path to the default template prefixed with './'.
 */
export function getTemplateUrl(env: { template?: string }, default_template: string): string {
    // If env.theme exists and is not an empty string, return the theme URL
    return env.template ? './' + default_template + '.' + env.template + '.component.html' : './' + default_template + '.default.component.html';
}




/**
 * The function getStyleClass takes two arguments:
 * env: The environment object (Example: HeaderNavEnvironment).
 * key: The key whose values you want to concatenate.
 * It first checks if the value associated with the key is an object. If it is, it uses Object.values() to extract the values of that object and joins them with a space (' ').
 * If it's not an object or the key doesn't exist, it returns an empty string.
 * 
 * @param env 
 * @param key 
 * @returns string | styles in a flat list with proper prefixes like hover:, focus:, etc.
 */
export function getStyleClass(config: any, key: string): string {
    const baseStyles: any = [];

    // Helper function to extract styles from an object
    function extractStyles(obj: any, prefix = '') {
        for (const prop in obj) {
            if (typeof obj[prop] === 'object') {
                // Recursively handle nested objects (like hover, focus, active)
                extractStyles(obj[prop], `${prefix}${prop}:`);
            } else {
                // Push the style with an optional prefix (e.g., hover:)
                baseStyles.push(`${prefix}${obj[prop]}`);
            }
        }
    }

    if (config[key]) {
        extractStyles(config[key]);
    }

    return baseStyles.join(' ');
}

/**
 * extractStyles: Recursively processes the object and collects styles in a flat list with proper prefixes like hover:, focus:, etc.
 * getStylesForAll: Loops over each key in the environment object (e.g., btnPrimary, btnPrimaryOutline) and constructs the concatenated string of styles for each key.
 * 
 * @param config 
 * @returns object
 */

export function getAllStyleClass(config: any): { [key: string]: string } {
    const stylesObject: { [key: string]: string } = {};

    // Helper function to extract styles from an object
    function extractStyles(obj: any, prefix = ''): string[] {
        const baseStyles = [];
        for (const prop in obj) {
            if (typeof obj[prop] === 'object') {
                // Recursively handle nested objects (like hover, focus, active)
                baseStyles.push(...extractStyles(obj[prop], `${prop}:`));
            } else {
                // Push the style with an optional prefix (e.g., hover:)
                baseStyles.push(`${prefix}${obj[prop]}`);
            }
        }
        return baseStyles;
    }

    // Loop through each key in the configuration (e.g., btnPrimary, btnPrimaryOutline)
    for (const key in config) {
        if (config.hasOwnProperty(key)) {
            // Extract styles for each key
            stylesObject[key] = extractStyles(config[key]).join(' ');
        }
    }

    return stylesObject;
}


// string-utils.ts

/**
 * Converts the first letter of a word to uppercase and the rest to lowercase.
 * @param word - The string to capitalize.
 * @returns A string with the first letter capitalized.
 */
export function capitalize(word: string): string {
    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
}

/**
 * Converts all characters to lowercase.
 * @param word - The string to convert to lowercase.
 * @returns A string in lowercase.
 */
export function toLowerCase(word: string): string {
    return word.toLowerCase();
}

/**
 * Converts all characters to uppercase.
 * @param word - The string to convert to uppercase.
 * @returns A string in uppercase.
 */
export function toUpperCase(word: string): string {
    return word.toUpperCase();
}

/**
 * Converts a string to title case (first letter of each word capitalized).
 * @param sentence - The string to convert to title case.
 * @returns A string in title case.
 */
export function toTitleCase(sentence: string): string {
    return sentence.split(' ')
        .map(word => capitalize(word))
        .join(' ');
}

/**
 * Converts a string to sentence case (only the first letter of the first word is capitalized).
 * @param sentence - The string to convert to sentence case.
 * @returns A string in sentence case.
 */
export function toSentenceCase(sentence: string): string {
    return capitalize(sentence.toLowerCase());
}

/**
 * Converts a string to camelCase (first word lowercase, subsequent words capitalized).
 * @param sentence - The string to convert to camel case.
 * @returns A string in camel case.
 */
export function toCamelCase(sentence: string): string {
    return sentence.split(' ')
        .map((word, index) => index === 0 ? word.toLowerCase() : capitalize(word))
        .join('');
}

/**
 * Converts a string to PascalCase (every word capitalized with no spaces).
 * @param sentence - The string to convert to Pascal case.
 * @returns A string in Pascal case.
 */
export function toPascalCase(sentence: string): string {
    return sentence.split(' ')
        .map(word => capitalize(word))
        .join('');
}

/**
 * Converts a string to snake_case (all lowercase words separated by underscores).
 * @param sentence - The string to convert to snake case.
 * @returns A string in snake case.
 */
export function toSnakeCase(sentence: string): string {
    return sentence.toLowerCase().split(' ').join('_');
}

/**
 * Converts a string to kebab-case (all lowercase words separated by hyphens).
 * @param sentence - The string to convert to kebab case.
 * @returns A string in kebab case.
 */
export function toKebabCase(sentence: string): string {
    return sentence.toLowerCase().split(' ').join('-');
}

/**
 * Converts a string to aLtErNaTiNg CaSe (alternates upper and lower case).
 * @param sentence - The string to convert to alternating case.
 * @returns A string with alternating uppercase and lowercase letters.
 */
export function toAlternatingCase(sentence: string): string {
    return sentence.split('')
        .map((char, index) => index % 2 === 0 ? char.toLowerCase() : char.toUpperCase())
        .join('');
}



// src/app/utils/utils.ts
// export function deepMerge(target: any, source: any): any {
//     for (const key in source) {
//         if (source.hasOwnProperty(key)) {
//             if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
//                 if (!target[key]) {
//                     target[key] = {}; // Initialize if the target key is not present
//                 }
//                 deepMerge(target[key], source[key]); // Recursively merge nested objects
//             } else {
//                 target[key] = source[key]; // Directly assign the value for non-objects
//             }
//         }
//     }
//     return target;
// }

export function deepMerge(target: any, source: any): any {
    for (const key in source) {
        if (source.hasOwnProperty(key)) {
            // Check if the source value is an array
            if (Array.isArray(source[key])) {
                // Replace the target key with the source array
                target[key] = source[key];
            }
            // Check if the source value is an object (and not an array)
            else if (typeof source[key] === 'object' && source[key] !== null) {
                // Initialize if the target key is not present
                if (!target[key]) {
                    target[key] = {};
                }
                deepMerge(target[key], source[key]); // Recursively merge nested objects
            } else {
                target[key] = source[key]; // Directly assign the value for non-objects
            }
        }
    }
    return target;
}




/**
 * Retrieves a constant value from APP_CONSTANTS_TOKEN using Angular's Injector.
 * 
 * @param key - The key of the constant to retrieve.
 * @returns The corresponding constant value or undefined if not found.
 */
export function getConstantValue<T = any>(key?: string): T | undefined {
    const injector = inject(Injector); // Get Angular's global injector
    const constants = injector.get(APP_CONSTANTS_TOKEN, {}); // Get provided constants

    if (!key) {
        return constants as T; // Return all constants if no key is provided
    }

    return key.split('.').reduce((acc, curr) => acc && acc[curr], constants) as T;
}

/**
 * Extracts a list (array) of DTOs from a nested API response object.
 *
 * This utility is commonly used to safely extract an array from API responses
 * where the expected list is nested under `response.data[dtoListKey]`.
 *
 * - It checks if the value at the specified key is an array.
 * - If the value is not an array or doesn't exist, it returns a fallback array (default is an empty array).
 *
 * @template T - The type of objects in the resulting array.
 * @param response - The API response object (expected to have a `data` property).
 * @param dtoListKey - The key inside `response.data` where the array is expected (e.g., 'LanguageContainerDTOList').
 * @param fallback - Optional fallback value to return if the key does not contain a valid array (default: `[]`).
 * @returns The extracted array of DTOs or the fallback.
 *
 * @example
 * // Sample API response:
 * const response = {
 *   data: {
 *     LanguageContainerDTOList: [
 *       { LanguageId: 1, LanguageName: 'English' },
 *       { LanguageId: 2, LanguageName: 'Spanish' }
 *     ]
 *   }
 * };
 *
 * const list = extractDTOList<LanguageDTO>(response, 'LanguageContainerDTOList');
 * console.log(list.length); // Output: 2
 *
 * const invalidResponse = { data: null };
 * const fallbackList = extractDTOList<LanguageDTO>(invalidResponse, 'LanguageContainerDTOList');
 * console.log(fallbackList); // Output: []
 */
export function extractDTOList<T>(
  response: any,
  dtoListKey: string,
  fallback: T[] = []
): T[] {
  return Array.isArray(response?.data?.[dtoListKey])
    ? response.data[dtoListKey]
    : fallback;
}


/**
 * Compares two values for equality.
 *
 * - If both values are strings, performs a **case-insensitive** comparison.
 * - For all other types (number, boolean, etc.), uses strict equality (`===`).
 *
 * @param a - First value to compare.
 * @param b - Second value to compare.
 * @returns `true` if the values are equal (case-insensitive for strings), otherwise `false`.
 *
 * @example
 * areEqual('Hello', 'hello'); // true (case-insensitive string match)
 * areEqual('Hello', 'HELLO'); // true
 * areEqual('Hi', 'Bye');      // false
 * areEqual(5, 5);             // true
 * areEqual(true, false);      // false
 * areEqual(null, null);       // true
 */
export function areEqual<T>(a: T, b: T): boolean {
    if (typeof a === 'string' && typeof b === 'string') {
        return a.toLowerCase() === b.toLowerCase();
    }
    return a === b;
}

/**
 * ================================================
 * USAGE
 * ================================================
 *
 * getConstantValue();
 * getConstantValue('SAMPLE');
 * getConstantValue('API_ENDPOINTS.AUTHENTICATE_SYSTEM_USERS');
 * ================================================
 */



/**
 * Wrapper to retrieve API endpoint constants from APP_CONSTANTS_TOKEN.
 *
 * @param key - The key inside API_ENDPOINTS to retrieve.
 * @returns The corresponding API endpoint string or undefined if not found.
 */
// export function getApiEndPoint(key: string): string | undefined {
//     const apiEndpoints = getConstantValue<Record<string, string>>('API_ENDPOINTS');
//     return apiEndpoints?.[key];
// }


// export function getApiUrl(key: string): any {
//     const injector = inject(Injector); // Get Angular's global injector
//     const envService = injector.get(EnvService, {}); // Get provided constants

//     return envService.parafaitApiBaseUrl + getApiEndPoint(key);
// }

// export function getApiUrl(key: string): any {
//   return runInInjectionContext(inject(EnvironmentInjector), () => {
//     const envService = inject(EnvService);
//     return envService.parafaitApiBaseUrl + getApiEndPoint(key);
//   });
// }


/**
 * Debounce utility to delay the execution of a function until a certain amount of time has passed since the last invocation.
 * It is commonly used to prevent excessive function calls and improve performance.
 * @param input - The input signal to debounce.
 * @param delay - The delay in milliseconds.
 * @returns A signal that emits the debounced value.
 */
export function debounce<T>(input: Signal<T>, delay: number): Signal<T> {
    const output = signal(input());
    let timeout: ReturnType<typeof setTimeout>;

    effect((onCleanup) => {
        clearTimeout(timeout);
        const value = input();
        timeout = setTimeout(() => output.set(value), delay); 
        onCleanup(() => clearTimeout(timeout));
    });

    return output;
}