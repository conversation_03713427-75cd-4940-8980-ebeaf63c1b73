import { BehaviorSubject, Observable } from 'rxjs';

export class StateSubject<T> {
    private _subject: BehaviorSubject<T>;
    public readonly observable$: Observable<T>;

    constructor(initialValue: T) {
        this._subject = new BehaviorSubject<T>(initialValue);
        this.observable$ = this._subject.asObservable(); // Expose only Observable
    }

    // Method to update value
    set(value: T): void {
        this._subject.next(value);
    }

    // Method to get the current value
    get(): T {
        return this._subject.value;
    }
}

/**
 * ================================================
 * USAGE
 * ================================================
 * private _isLoggedIn = new StateSubject<boolean>(false);
 * 
 * // Expose only Observable, not the BehaviorSubject
 * isLoggedIn$ = this._isLoggedIn.observable$;
 * ================================================
 */


