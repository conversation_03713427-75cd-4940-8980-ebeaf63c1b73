/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-04-03
 */

import { InjectionToken } from '@angular/core';

// Create a token
export const PARENT_FUNCTION_TOKEN = new InjectionToken<any>('ParentFunctionToken');

export const DEFAULT_APP_CONFIG_TOKEN = new InjectionToken<Record<string, any>>('default_app_config_token');

export const APP_CONSTANTS_TOKEN = new InjectionToken<Record<string, any>>('app_constants_token');

// export const APP_ENVIRONMENT = new InjectionToken<Record<string, any>>('APP_ENVIRONMENT');
