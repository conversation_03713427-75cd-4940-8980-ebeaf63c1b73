/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
import { BaseDTO } from './base-dto.model';

export class ParafaitDefaultContainerDTOModel extends BaseDTO<ParafaitDefaultContainerDTOModel> {
    DefaultValueName: string = '';
    DefaultValue: string = '';

    constructor() {
        super();
    }

    /**
     * Find the DTO with the given name in the list (case-sensitive).
     * @param list  array of ParafaitDefaultContainerDTOModel
     * @param name  the DefaultValueName to look for
     * @returns the matching DTO or `undefined`
     */
    static findByName(
        list: ParafaitDefaultContainerDTOModel[],
        name: string
    ): ParafaitDefaultContainerDTOModel | undefined {
        return list.find(item => item.DefaultValueName === name);
    }

    /**
     * Get the DefaultValue for the given name. Returns `null` if not found.
     * @param list  array of ParafaitDefaultContainerDTOModel
     * @param name  the DefaultValueName to look for
     * @returns the DefaultValue string, or `null` if no matching item
     */
    static getValueByName(
        list: ParafaitDefaultContainerDTOModel[],
        name: string
    ): string | null {
        const item = ParafaitDefaultContainerDTOModel.findByName(list, name);
        return item ? item.DefaultValue : null;
    }
}

/**
// imagine you already deserialized your array:
const rawList = response.data.ParafaitDefaultContainerDTOList;
const dtoList = ParafaitDefaultContainerDTOModel.fromList(rawList);

// get the URL for inventory upload service
const inventoryUrl = ParafaitDefaultContainerDTOModel.getValueByName(
  dtoList,
  'INVENTORY_UPLOAD_SERVICE_URL'
);
console.log(inventoryUrl); // "" (or whatever the API provided)

// find the entire DTO for SUCCESS_EMAIL_LIST
const successEmailDto = ParafaitDefaultContainerDTOModel.findByName(
  dtoList,
  'SUCCESS_EMAIL_LIST'
);
console.log(successEmailDto?.DefaultValue);
 */

