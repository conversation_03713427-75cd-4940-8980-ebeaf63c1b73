/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
import { BaseDTO } from './base-dto.model';

export class SiteViewsDTOModel extends BaseDTO<SiteViewsDTOModel> {
    CurrentDateTime: string = '';
    OnlineOrderDeliveryIntegrationContainerDTOList: any[] | null = null;
    SiteId: number = 0;
    SiteName: string = '';
    SiteAddress: string = '';
    IsMasterSite: boolean = false;
    OnlineEnabled: boolean = false;
    PinCode: string = '';
    SiteURL: string = '';
    SiteShortName: string = '';
    City: string = '';
    State: string = '';
    Country: string = '';
    StoreType: string = '';
    Logo: string | null = null;
    StoreRanking: string = '';
    OpenTime: number = 0;
    CloseTime: number = 0;
    OpenDate: string = '';
    ClosureDate: string = '';
    BusinessDayStartTime: number = 0;
    TimeZoneName: string = '';
    Email: string = '';
    PhoneNumber: string = '';
    CustomerKey: string = '';
    Latitude: number | null = null;
    Longitude: number | null = null;
    SiteDeliveryDetailsDTOList: any[] | null = null;
    Version: string = '';

    constructor() {
        super();
        // All defaults are set above; no further manual assignment needed.
    }

    /**
     * Find the DTO with the given SiteId.
     * @param list  An array of SiteViewsDTOModel instances
     * @param id    The SiteId to look for
     * @returns     The matching DTO or `undefined` if not found
     */
    static findBySiteId(
        list: SiteViewsDTOModel[],
        id: number
    ): SiteViewsDTOModel | undefined {
        return list.find(item => item.SiteId === id);
    }

    /**
     * Convenience to get the SiteName for a given SiteId (or null if not found).
     * @param list  An array of SiteViewsDTOModel instances
     * @param id    The SiteId to look for
     * @returns     The SiteName string or `null` if no matching site
     */
    static getSiteNameById(
        list: SiteViewsDTOModel[],
        id: number
    ): string | null {
        const site = SiteViewsDTOModel.findBySiteId(list, id);
        return site ? site.SiteName : null;
    }
}
