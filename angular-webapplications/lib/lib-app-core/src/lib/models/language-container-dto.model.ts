/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
import { BaseDTO } from './base-dto.model';

export class LanguageContainerDTOModel extends BaseDTO<LanguageContainerDTOModel> {
    LanguageId: number    = 0;
    LanguageName: string  = '';
    LanguageCode: string  = '';
    CultureCode: string   = '';

    constructor() {
        super();
    }

    get displayName(): string {
        return `${this.LanguageName} (${this.LanguageCode})`;
    }

    hasCultureCode(): boolean {
        return this.CultureCode.trim().length > 0;
    }
}
