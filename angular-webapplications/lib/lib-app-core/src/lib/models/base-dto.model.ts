/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
export abstract class BaseDTO<T extends object> {
    constructor() {
        /* no-op */
    }

    /**
     * Create a single DTO instance from a plain object.
     * - First, `new this()` runs the subclass’s field initializers (setting defaults).
     * - Then `Object.assign` overwrites only those defaults with whatever keys appear in `input`.
     */
    static fromSingle<T extends object>(this: new () => T, input: Partial<T>): T {
        const instance = new this();
        if (input) {
            Object.assign(instance, input);
        }
        return instance;
    }

    /**
     * Create an array of DTO instances from either a single object or an array of objects.
     */
    static fromList<T extends object>(
        this: new () => T,
        input: Partial<T> | Partial<T>[]
    ): T[] {
        if (!input) return [];
        const arr = Array.isArray(input) ? input : [input];
        return arr.map(item => {
            const inst = new this();
            if (item) {
                Object.assign(inst, item);
            }
            return inst;
        });
    }
}
