/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-03-25
 */
import { <PERSON>rror<PERSON><PERSON><PERSON>, Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class GlobalExceptionHandler implements ErrorHandler {
  handleError(error: any): void {
    console.error('Global Exception Caught:', error);

    // TODO: Log error to a server, show a user-friendly message, etc.
  }
}
