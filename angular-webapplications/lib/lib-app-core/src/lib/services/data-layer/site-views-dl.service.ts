/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { Injectable } from '@angular/core';
import { Observable, tap } from 'rxjs';
import { ApiServiceBase } from './api-service-base-dl.service';
import { SiteViewsDTOModel } from '../../models/site-views-dto.model';
import { extractDTOList } from 'lib-app-core';

@Injectable({ providedIn: 'root' })
export class SiteViewsServiceDL extends ApiServiceBase {
    private _apiData: any;
    constructor() {
        super('site_views_data', 'SITE_VIEWS_JSON')
        this.init();
    }

    load(): Observable<any> {
         const url = `${this.getApiUrl()}?buildChildRecords=true`
        //const url = this.getJSONApiUrl()
        return this._http.get<any>(url).pipe(
            tap(response => {   
                // const dtoList = extractDTOList<SiteViewsDTOModel>(response, 'SiteViewsDTOModel');
                const raw = response.data
                this._apiData = SiteViewsDTOModel.fromList(raw);

                // console.log(this._apiData)
                this.storeData(this._apiData); // Store only the list, or full response if needed
            })
        );
    }
}