/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { Injectable } from '@angular/core';
import { Observable, tap } from 'rxjs';
import { ApiServiceBase } from './api-service-base-dl.service';
import { AuthenticateSystemUsersDTOModel } from '../../models/authenticate-system-users-dto.model';

export type AuthenticateSystemUsersServerApiParams = {
    siteId: number
}

@Injectable({ providedIn: 'root' })
export class AuthenticateSystemUsersServerServiceDL extends ApiServiceBase {
    private apiParams!: AuthenticateSystemUsersServerApiParams;
    private _apiData: any;

    constructor() {
        super('authenticate_system_users_data', 'AUTHENTICATE_SYSTEM_USERS')
        this.init();
    }

    buildApiParams(data: AuthenticateSystemUsersServerApiParams) {
        this.apiParams = data
    }

    load(): Observable<any> {
        const url = `${this.getApiUrl()}`;
        const loginPayload = this.apiParams;

        return this._http.post<any>(url, loginPayload).pipe(
            tap(response => {
                // const dtoList = response?.data;
                const raw = response?.data; // { PosMachineGuid: "...", WebApiToken: "...", … }
                this._apiData = AuthenticateSystemUsersDTOModel.fromSingle(raw);
                // console.log(this._apiData)
                this.storeData(this._apiData); // store for client
            })
        );
    }
}


// this.authenticateSystemUsersService.load().subscribe(() => {
//   const token = this.authenticateSystemUsersService.getWebApiToken();
//   const langId = this.authenticateSystemUsersService.getLanguageId();
//   console.log({ token, langId });
// });