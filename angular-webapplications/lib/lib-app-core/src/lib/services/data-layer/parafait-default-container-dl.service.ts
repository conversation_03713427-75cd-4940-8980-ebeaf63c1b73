/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { inject, Injectable } from '@angular/core';
import { Observable, tap } from 'rxjs';
import { ApiServiceBase } from './api-service-base-dl.service';
import { HttpParams } from '@angular/common/http';
import { DEFAULT_APP_CONFIG_TOKEN, extractDTOList } from 'lib-app-core';
import { ParafaitDefaultContainerDTOModel } from '../../models/parafait-default-container-dto.model';

export type ParafaitDefaultContainerApiParams = {
    siteId: number
}

@Injectable({ providedIn: 'root' })
export class ParafaitDefaultContainerService extends ApiServiceBase {
    private apiParams!: ParafaitDefaultContainerApiParams;
    private _apiData: any;
    constructor() {
        super('parafait_default_container_data', 'PARAFAIT_DEFAULT_CONTAINER')
        this.init();
    }

    buildApiParams(data: ParafaitDefaultContainerApiParams) {
        this.apiParams = data
    }

    load(): Observable<any> {
        let params = new HttpParams()
            .set('siteId', this.apiParams?.siteId.toString())
            .set('userPkId', '-1')
            .set('machineId', '353')
            .set('rebuildCache', 'false')
            .set('defaultValueNames',
                'DATE_FORMAT,ENABLE_GOOGLE_RECAPTCHA,GOOGLE_RECAPTCHA_CLIENT_ID,AMOUNT_FORMAT,' +
                'CURRENCY_CODE,CURRENCY_SYMBOL,AMOUNT_FORMAT_UI,DATETIME_FORMAT,NUMBER_FORMAT_UI,' +
                'CUSTOMER_REGISTRATION_SECURITY_POLICY,CARD_EXPIRY_RULE,CARD_VALIDITY,ENABLE_ADDRESS_VALIDATION,' +
                'CUSTOMER_PHONE_NUMBER_WIDTH'
            );

        const url = `${this.getApiUrl()}?buildChildRecords=true`
        return this._http.get<any>(url, { params }).pipe(
            tap(response => {
                const dtoList = extractDTOList<ParafaitDefaultContainerDTOModel>(response, 'ParafaitDefaultContainerDTOList');

                this._apiData = ParafaitDefaultContainerDTOModel.fromList(dtoList);
                this.storeData(this._apiData); // Store only the list, or full response if needed

            })
        );
    }
}