/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, filter, Observable } from 'rxjs';
import { DataTransferService, EnvService } from 'lib-app-core';

@Injectable({ providedIn: 'root' })
export abstract class ApiServiceBase {
    protected _dataSubject = new BehaviorSubject<any | null>(null);
    protected _http = inject(HttpClient)
    protected _envService = inject(EnvService);

    private _dataTransferService = inject(DataTransferService)
    // private _dataTransferKey: string = '';
    // private _apiEndpoint: string = '';

    abstract load(): Observable<any>;
    // protected abstract getDataTransferKey(): string;
    // protected abstract getApiEndpoint(): string;

    constructor(
        private _dataTransferKey: string,
        private _apiEndpoint: string
    ) {
        // this.dataTransferKey$ = this._dataTransferKey;
        // this.apiEndpoint$ = this._apiEndpoint;
        // this.init()
    }

    init() {
        const cached = this._dataTransferService.getData(this.dataTransferKey$, null);
        if (cached) {
            this._dataSubject.next(cached);
        }
    }

    get data$(): Observable<any | null> {
        return this._dataSubject.asObservable();
    }

    protected get dataTransferKey$(): string {
        return this._dataTransferKey;
    }

    protected set dataTransferKey$(key: string) {
        this._dataTransferKey = key;
    }

    protected get apiEndpoint$(): string {
        return this._apiEndpoint;
    }

    protected set apiEndpoint$(key: string) {
        this._apiEndpoint = key;
    }

    /**
     * Subscribe to data changes with a callback.
     * Automatically skips null values if needed.
     */

    subscribeToData(callback: (data: any) => void) {
        return this.data$.pipe(
            filter(data => data !== null)  // skip null emissions
        ).subscribe(callback);
    }

    protected getApiUrl(params?: { [key: string]: string | number }) {
        return this._envService.getApiUrl(this.apiEndpoint$, params);
    }

    protected getNodeApiUrl(params?: { [key: string]: string | number }) {
        return this._envService.getNodeApiUrl(this.apiEndpoint$, params);
    }

    protected getJsonApiUrl(params?: { [key: string]: string | number }) {
        return this._envService.getJsonApiUrl(this.apiEndpoint$, params);
    }

    protected storeData(data: any) {
        this._dataTransferService.setData(this.dataTransferKey$, data);
        this._dataSubject.next(data);
    }

    // getRandomInt(min: number, max: number) {
    //     return Math.floor(Math.random() * (max - min + 1)) + min;
    // }

    // get data(): any | null {
    //     return this.dataTransferService.getData(LANGUAGE_CONTAINER_KEY, null);
    // }
}


//  Usage:
//  this.service.subscribeToData(data => {
//     this.localVar = data;
// });
