/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { inject, Injectable } from '@angular/core';
import { APP_CONSTANTS_TOKEN } from 'lib-app-core';

@Injectable({ providedIn: 'root' })
export class AppConstantService {

    private appConstants = inject(APP_CONSTANTS_TOKEN)

    constructor() {

    }

    getConstantValue<T = any>(key?: string): T | undefined {
        if (!key) {
            return this.appConstants as T; // Return all constants if no key is provided
        }

        return key.split('.').reduce((acc, curr) => acc && acc[curr], this.appConstants) as T;
    }

    getApiEndPoint(key: string): string | undefined {
        const apiEndpoints = this.getConstantValue<Record<string, string>>('API_ENDPOINTS');
        return apiEndpoints?.[key];
    }
}