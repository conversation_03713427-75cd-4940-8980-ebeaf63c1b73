import { TestBed } from '@angular/core/testing';
import { PLATFORM_ID } from '@angular/core';
import { CookieService } from './cookie.service';

const clearCookies = () => {
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const cookieParts = cookie.split('=');
        const hasEqualsSign = cookieParts.length > 1;

        const cookieName = hasEqualsSign
            ? cookieParts[0].trim()
            : cookie.trim();

        // Delete the cookie by setting it to an expired date
        document.cookie = `${encodeURIComponent(
            cookieName
        )}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
    }
};

describe('CookieService', () => {
    let cookieService: CookieService;

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                CookieService,
                { provide: PLATFORM_ID, useValue: 'browser' },
            ],
        });
        cookieService = TestBed.inject(CookieService);
    });

    afterEach(() => {});
    clearCookies();
    it('should be created', () => {
        expect(cookieService).toBeTruthy();
    });

    it('should set and get a cookie', () => {
        cookieService.setCookie('testName', 'testValue');
        expect(cookieService.getCookie('testName')).toBe('testValue');
    });

    it('should return null for non-existent cookie', () => {
        expect(cookieService.getCookie('nonExistent')).toBeNull();
    });

    it('should delete a cookie', () => {
        cookieService.setCookie('toDelete', 'value', { path: '/' });
        cookieService.deleteCookie('toDelete', '/');
        expect(cookieService.getCookie('toDelete')).toBeNull();
    });

    it('should confirm cookie existence', () => {
        cookieService.setCookie('exists', 'yes');
        expect(cookieService.hasCookie('exists')).toBeTrue();
        expect(cookieService.hasCookie('missing')).toBeFalse();
    });

    it('should store and retrieve a cookie as object', () => {
        const obj = { a: 1, b: 'two' };
        cookieService.setCookieAsObject('jsonCookie', obj);
        expect(cookieService.getCookieAsObject('jsonCookie')).toEqual(obj);
    });

    it('should set cookie with object and options', () => {
        const obj = { x: 123 };
        cookieService.setCookieAsObject('objectWithOptions', obj, {
            path: '/',
            sameSite: 'Lax',
        });
        expect(cookieService.getCookieAsObject('objectWithOptions')).toEqual(
            obj
        );
    });

    it('should return null for invalid JSON in getCookieAsObject', () => {
        cookieService.setCookie('badJson', '%7Bbad'); // corrupted value
        expect(cookieService.getCookieAsObject('badJson')).toBeNull();
    });

    it('should get all cookies as object', () => {
        cookieService.setCookie('cookie1', 'val1');
        cookieService.setCookie('cookie2', 'val2');
        const all = cookieService.getAllCookies();
        expect(all['cookie1']).toBe('val1');
        expect(all['cookie2']).toBe('val2');
    });

    it('should throw error if SameSite=None is used without Secure', () => {
        expect(() => {
            cookieService.setCookie('crossSite', 'token', {
                sameSite: 'None',
                secure: false, // insecure on purpose
            });
        }).toThrowError(
            "Cookies with SameSite=None must also set 'secure: true' or they will be rejected by modern browsers."
        );
    });

    it('should allow SameSite=None when Secure is true', () => {
        expect(() => {
            cookieService.setCookie('safeCrossSite', 'token', {
                sameSite: 'None',
                secure: true,
            });
        }).not.toThrow();
        expect(cookieService.getCookie('safeCrossSite')).toBe('token');
    });
});
