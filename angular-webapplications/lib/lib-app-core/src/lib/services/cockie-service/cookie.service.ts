import { Injectable, inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

export interface CookieOptions {
    expires?: Date | number;
    path?: string;
    domain?: string;
    secure?: boolean;
    sameSite?: 'Strict' | 'Lax' | 'None';
}

@Injectable({ providedIn: 'root' })
export class CookieService {
    private readonly platformId = inject(PLATFORM_ID);

    /**
     * Checks if the code is running in a browser environment
     * @returns True if running in browser, false otherwise
     */
    private isBrowser(): boolean {
        return isPlatformBrowser(this.platformId);
    }

    /**
     * Sets a cookie with the given name, value, and options
     * @param name - Cookie name
     * @param value - Cookie value
     * @param options - Cookie options (expires, path, domain, secure, sameSite)
     */
    setCookie(name: string, value: string, options: CookieOptions = {}): void {
        if (!this.isBrowser()) {
            return;
        }

        let cookieString = `${encodeURIComponent(name)}=${encodeURIComponent(
            value
        )}`;

        if (options.expires) {
            const expires =
                options.expires instanceof Date
                    ? options.expires
                    : new Date(options.expires);
            cookieString += `; expires=${expires.toUTCString()}`;
        }

        if (options.path) {
            cookieString += `; path=${options.path}`;
        }

        if (options.domain) {
            cookieString += `; domain=${options.domain}`;
        }

        if (options.secure) {
            cookieString += '; secure';
        }

        if (options.sameSite) {
            if (options.sameSite === 'None' && !options.secure) {
                throw new Error(
                    "Cookies with SameSite=None must also set 'secure: true' or they will be rejected by modern browsers."
                );
            }
            cookieString += `; samesite=${options.sameSite.toLowerCase()}`;
        }

        document.cookie = cookieString;
    }

    /**
     * Gets a cookie value by name
     * @param name - Cookie name
     * @returns Cookie value or null if not found
     */
    getCookie(name: string): string | null {
        if (!this.isBrowser()) {
            return null;
        }

        const nameEQ = encodeURIComponent(name) + '=';
        const cookies = document.cookie.split(';');

        for (let cookie of cookies) {
            cookie = cookie.trim();
            if (cookie.indexOf(nameEQ) === 0) {
                return decodeURIComponent(cookie.substring(nameEQ.length));
            }
        }

        return null;
    }

    /**
     * Gets all cookies as a key-value object
     * @returns Object containing all cookies
     */
    getAllCookies(): Record<string, string> {
        if (!this.isBrowser()) {
            return {};
        }

        const cookies: Record<string, string> = {};
        const cookieArray = document.cookie.split(';');

        for (let cookie of cookieArray) {
            cookie = cookie.trim();
            const separatorIndex = cookie.indexOf('=');
            if (separatorIndex > 0) {
                const name = decodeURIComponent(
                    cookie.substring(0, separatorIndex)
                );
                const value = decodeURIComponent(
                    cookie.substring(separatorIndex + 1)
                );
                cookies[name] = value;
            }
        }

        return cookies;
    }

    /**
     * Deletes a cookie by setting its expiration to the past
     * @param name - Cookie name
     * @param path - Cookie path (must match the path used when setting)
     * @param domain - Cookie domain (must match the domain used when setting)
     */
    deleteCookie(name: string, path?: string, domain?: string): void {
        if (!this.isBrowser()) {
            return;
        }

        const options: CookieOptions = {
            expires: new Date(0),
            path: path || '/',
            domain,
        };

        this.setCookie(name, '', options);
    }

    /**
     * Checks if a cookie exists
     * @param name - Cookie name
     * @returns True if cookie exists, false otherwise
     */
    hasCookie(name: string): boolean {
        return this.getCookie(name) !== null;
    }

    /**
     * Gets a cookie and parses it as JSON
     * @param name - Cookie name
     * @returns Parsed JSON object or null if cookie doesn't exist or is invalid JSON
     */
    getCookieAsObject<T = any>(name: string): T | null {
        const cookieValue = this.getCookie(name);
        if (!cookieValue) {
            return null;
        }

        try {
            return JSON.parse(cookieValue) as T;
        } catch {
            return null;
        }
    }

    /**
     * Sets a cookie with JSON value
     * @param name - Cookie name
     * @param value - Object to be stored as JSON
     * @param options - Cookie options
     */
    setCookieAsObject<T>(
        name: string,
        value: T,
        options: CookieOptions = {}
    ): void {
        const jsonValue = JSON.stringify(value);
        this.setCookie(name, jsonValue, options);
    }
}
