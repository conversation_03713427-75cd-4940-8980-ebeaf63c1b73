/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { BehaviorSubject, Observable, distinctUntilChanged } from "rxjs";

export class DataStreamService<T> {
    private dataSubject: BehaviorSubject<T | null>;
    public data$: Observable<T | null>;

    constructor() {
        this.dataSubject = new BehaviorSubject<T | null>(null);
        this.data$ = this.dataSubject.asObservable().pipe(distinctUntilChanged());
    }

    initialize(value: T): void {
        this.dataSubject.next(value);
    }

    setData(value: T): void {
        this.dataSubject.next(value);
    }

    getData(): T | null {
        return this.dataSubject.getValue();
    }

    clearData(): void {
        this.dataSubject.next(null);
    }
}
