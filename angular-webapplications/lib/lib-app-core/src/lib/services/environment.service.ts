/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { inject, Injectable, makeStateKey, TransferState } from "@angular/core";
import { AppConstantService } from "lib-app-core";

/**
 * Unique key to store and retrieve environment variables using <PERSON><PERSON>'s TransferState.
 * This enables transferring server-side rendered state to the client efficiently.
 */
const ENV_VARS_KEY = makeStateKey<{ [key: string]: string }>('envVars');

/**
 * EnvService provides access to environment variables that are transferred
 * from the server to the client using <PERSON><PERSON>'s TransferState.
 *
 * This service ensures that environment variables are available on both
 * server-side and client-side rendering, optimizing SSR (Server-Side Rendering) performance.
 */
@Injectable({
    providedIn: 'root',
})
export abstract class EnvService {
    /**
     * Injecting Angular's TransferState to store and retrieve transferred state.
     */
    private transferState = inject(TransferState);
    private appConstantService = inject(AppConstantService);

    /**
     * Stores the environment variables retrieved from TransferState.
     */
    private envVars: { [key: string]: string };

    constructor() {
        // Retrieve the environment variables from TransferState
        // If not available, initialize with an empty object to prevent errors.
        this.envVars = this.transferState.get(ENV_VARS_KEY, {});
        // console.log(this.envVars); // Uncomment for debugging
    }

    /**
     * Retrieves the base URL for the Parafait API from the environment variables.
     * @returns {string} The base URL of the Parafait API.
     */
    get parafaitApiBaseUrl(): string {
        return this.envVars['PARAFAIT_API_BASEURL'] || '';
    }

    /**
     * Retrieves the base URL for the application's Node.js API from the environment variables.
     * @returns {string} The base URL of the Node.js API.
     */
    get appNodeApiBaseUrl(): string {
        return this.envVars['APP_NODEAPI_BASEURL'] || '';
    }

    get appJsonApiBaseUrl(): string {
        return this.envVars['API_JSON_BASEURL'] || '';
    }

    // getApiUrl(key: string): string{
    //     return this.parafaitApiBaseUrl + this.appConstantService.getApiEndPoint(key);
    // }

    getApiEndpoint(key: string, params?: { [key: string]: string | number }): string {
        const endpoint = this.appConstantService.getApiEndPoint(key);

        if (!endpoint) {
            throw new Error(`API endpoint not found for key: ${key}`);
        }

        let finalEndpoint = endpoint;
        if (params) {
            for (const [paramKey, paramValue] of Object.entries(params)) {
                finalEndpoint = finalEndpoint.replace(new RegExp(`{${paramKey}}`, 'g'), encodeURIComponent(String(paramValue)));
            }
        }

        return finalEndpoint;
    }

    getApiUrl(key: string, params?: { [key: string]: string | number }): string {
        const finalEndpoint = this.getApiEndpoint(key, params);
        return this.parafaitApiBaseUrl + finalEndpoint;
    }

    getNodeApiUrl(key: string, params?: { [key: string]: string | number }): string {
        const finalEndpoint = this.getApiEndpoint(key, params);
        return this.appNodeApiBaseUrl + finalEndpoint;
    }

    getJsonApiUrl(key: string, params?: { [key: string]: string | number }): string {
        const finalEndpoint = this.getApiEndpoint(key, params);
        return this.appJsonApiBaseUrl + finalEndpoint;
    }

}

/**
 * Usage Example:
 *
 * ```typescript
 * import { Component } from '@angular/core';
 * import { EnvService } from './env.service';
 *
 * @Component({
 *   selector: 'app-example',
 *   template: '<p>API Base URL: {{ apiUrl }}</p>'
 * })
 * export class ExampleComponent {
 *   apiUrl: string;
 *   
 *   envService = inject(EnvService)
 *   constructor() {
 *     this.apiUrl = this.envService.parafaitApiBaseUrl;
 *   }
 * }
 * ```
 */



// this.getApiUrl('CREATE_CUSTOMER_WAIVER', {
//     custId: 12345,
//     selectedWaiverSetId: 67890
// });