/**
 * @fileoverview 
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-04-02
 */
import { Injectable, TransferState, makeStateKey, StateKey } from '@angular/core';

/**
 * DataTransferService acts as a wrapper around Angular's TransferState API,
 * providing easy methods to store and retrieve server-side rendered data.
 * This prevents redundant API calls on the client side and improves performance.
 */
@Injectable({
    providedIn: 'root'
})
export class DataTransferService {
    constructor(private transferState: TransferState) { }

    /**
     * Stores data in TransferState.
     * This is typically used on the server during SSR to pass data to the client.
     * 
     * @param key - A unique string identifier for the stored data.
     * @param data - The data to be stored (must be serializable).
     */
    setData<T>(key: string, data: T): void {
        const stateKey = makeStateKey<T>(key);
        this.transferState.set(stateKey, data);
    }

    /**
     * Retrieves data from TransferState.
     * This is used on the client to access preloaded data and avoid duplicate HTTP requests.
     * 
     * @param key - The unique string identifier for the stored data.
     * @param defaultValue - A fallback value if the key is not found.
     * @returns The stored data or the default value if not available.
     */
    getData<T>(key: string, defaultValue: T): T {
        const stateKey = makeStateKey<T>(key);
        return this.transferState.get(stateKey, defaultValue);
    }

    /**
     * Checks if a specific key exists in TransferState.
     * This helps to determine whether data is available before fetching.
     * 
     * @param key - The unique string identifier for the stored data.
     * @returns True if the key exists, otherwise false.
     */
    hasKey(key: string): boolean {
        const stateKey = makeStateKey(key);
        return this.transferState.hasKey(stateKey);
    }

    /**
     * Removes a specific key and its associated data from TransferState.
     * This is useful for clearing state once it is no longer needed.
     * 
     * @param key - The unique string identifier for the stored data.
     */
    removeKey(key: string): void {
        const stateKey = makeStateKey(key);
        this.transferState.remove(stateKey);
    }

    /**
     * Clears all stored data from TransferState.
     * This is useful for resetting the state when necessary.
     */
    clear(): void {
        this.transferState.toJson(); // Calling toJson ensures TransferState is serialized before clearing.
        (this.transferState as any).store = {};
    }

    /**
     * Checks if TransferState is empty.
     * @returns True if no keys are stored, otherwise false.
     */
    isEmpty(): boolean {
        return this.transferState.isEmpty;
    }

    /**
     * Registers a callback function to provide a value for a key when `toJson()` is called.
     * This helps customize the serialization process.
     * 
     * @param key - The unique string identifier for the stored data.
     * @param callback - A function that returns the value to be serialized.
     */
    onSerialize<T>(key: string, callback: () => T): void {
        const stateKey = makeStateKey<T>(key);
        this.transferState.onSerialize(stateKey, callback);
    }

    /**
     * Serializes the current state into a JSON string.
     * This can be used to transfer the state from the server to the client.
     * 
     * @returns A JSON string representation of the stored state.
     */
    toJson(): string {
        return this.transferState.toJson();
    }
}


/**
 * ================================================
 * USAGE
 * ================================================
 * 
 * // Storing data (Server-side rendering)
 * this.dataTransferService.setData('userInfo', { name: 'John', age: 30 });
 * 
 * // Retrieving data (Client-side)
 * const userInfo = this.dataTransferService.getData('userInfo', null);
 * console.log(userInfo); // Output: { name: 'John', age: 30 }
 * 
 * // Checking if data exists before fetching
 * if (this.dataTransferService.hasKey('userInfo')) {
 *   console.log('User info is available in TransferState.');
 * }
 * ================================================
 */