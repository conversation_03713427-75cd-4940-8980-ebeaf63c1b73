/**
 * @fileoverview This is the main landing component (home page) for the application.
 * ThemeService provides utilities to retrieve and apply styles for different UI components.
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-09-18
 */

import { Injectable } from '@angular/core';
import { convertToTailwindClassList, SemnoxDefaultTheme } from 'lib-app-core'; // Utility function to convert objects to Tailwind CSS class strings
import { GlobalTheme } from '@theme/common/customer.theme'; // Import global theme styles
import { ElemSizeTheme } from '../config/element.size.theme'; // Import size-related styles for elements (like buttons)

@Injectable({
    providedIn: 'root' // This makes the service available throughout the application
})
export class ThemeService {
    // Stores the site-specific theme that will be applied to various components
    private siteTheme: any;
    // private semnoxDefaultTheme: any;
    private globalTheme: any;

    constructor() {
        // this.semnoxDefaultTheme = SemnoxDefaultTheme;
        this.globalTheme = GlobalTheme;
    }

    /**
     * Retrieves the Tailwind CSS classes for buttons based on the theme and style.
     * 
     * @param relevantTheme - Theme object specific to the current component or page
     * @param btnStyle - Button style (e.g., 'primary', 'accent', 'primary_stroked', etc.)
     * @param options - Optional object containing button customization options (e.g., size or button ID)
     * @returns string - A concatenated string of Tailwind CSS classes
     */
    getButtonClasses(relevantTheme: any, btnStyle: string, options?: any): string {
        const btnStylePrefix = 'btn_'; // Prefix used for button styles (e.g., 'btn_primary')
        const btnStyleName = btnStylePrefix + btnStyle; // Full style name (e.g., 'btn_primary')

        // Set default values for button size and ID
        let btnSize = 'base'; // Default size
        let btnId = ''; // Default ID
        if (options) {
            // Override defaults if options are provided
            btnSize = options['size'] ? options['size'] : btnSize;
            btnId = options['id'] ? options['id'] : '';
        }

        // Fetch size-specific styles from the ElemSizeTheme based on the button size
        const elemSize = ElemSizeTheme[btnStylePrefix + btnSize];

        // Fetch the button styles from the global theme, site theme, and page-specific theme
        // const semnoxDefaultTheme = this.semnoxDefaultTheme[btnStyleName];
        const globalTheme = this.globalTheme[btnStyleName];
        const siteTheme = this.siteTheme[btnStyleName];
        const pageSpecificTheme = relevantTheme[btnStyleName] || {};

        // Merge the themes, with priority given to page-specific and site-specific styles over global styles
        // let mergedTheme = { ...semnoxDefaultTheme, ...globalTheme, ...siteTheme, ...pageSpecificTheme };
        let mergedTheme = { ...globalTheme, ...siteTheme, ...pageSpecificTheme };

        // If a button ID is provided, fetch the button-specific theme and merge it
        if (btnId) {
            const btnIdTheme = relevantTheme[btnId];
            // mergedTheme = { ...semnoxDefaultTheme, ...globalTheme, ...siteTheme, ...pageSpecificTheme, ...btnIdTheme };
            mergedTheme = { ...globalTheme, ...siteTheme, ...pageSpecificTheme, ...btnIdTheme };
        }

        // Convert the merged theme into a list of Tailwind CSS classes using the utility function
        return `${convertToTailwindClassList(mergedTheme)} ${convertToTailwindClassList(elemSize)}`;
    }

    /**
     * Retrieves Tailwind CSS classes for input fields based on the theme and element ID.
     * 
     * @param relevantTheme - Theme object specific to the current component or page
     * @param elementId - Optional ID for specific input field customization
     * @returns string - A concatenated string of Tailwind CSS classes
     */
    getInputClass(relevantTheme: any, elementId?: string): any {
        // Fetch input styles from global theme, site theme, and page-specific theme
        // const semnoxDefaultTheme = this.semnoxDefaultTheme['input'];
        const globalTheme = this.globalTheme['input'];
        const siteTheme = this.siteTheme['input'];
        const pageSpecificTheme = relevantTheme['input'] || {};

        // Merge the themes, with priority given to page-specific and site-specific styles over global styles
        // let mergedTheme = { ...semnoxDefaultTheme, ...globalTheme, ...siteTheme, ...pageSpecificTheme };
        let mergedTheme = { ...globalTheme, ...siteTheme, ...pageSpecificTheme };

        // If an element ID is provided, fetch the element-specific theme and merge it
        if (elementId) {
            const elementIdTheme = relevantTheme[elementId];
            mergedTheme = { ...globalTheme, ...siteTheme, ...pageSpecificTheme, ...elementIdTheme };
        }

        // Convert the merged theme into a list of Tailwind CSS classes using the utility function
        return convertToTailwindClassList(mergedTheme);
    }

    /**
     * Retrieves Tailwind CSS classes for headers based on the theme and section name.
     * 
     * @param relevantTheme - Theme object specific to the current component or page
     * @param section - The section of the page (e.g., 'header', 'subheader')
     * @returns string - A concatenated string of Tailwind CSS classes for the header
     */
    getHeaderClass(relevantTheme: any, section: string) {
        // Fetch header styles from global theme, site theme, and page-specific theme for the given section
        // const semnoxDefaultTheme = this.semnoxDefaultTheme['header'][section];
        const globalTheme = this.globalTheme['header'] ? this.globalTheme['header'][section] : {};
        const siteTheme = this.siteTheme['header'] ? this.siteTheme['header'][section] : {};
        const pageSpecificTheme = relevantTheme['header'] ? relevantTheme['header'][section] : {};

        // Merge the themes, with priority given to page-specific and site-specific styles over global styles
        // let mergedTheme = { ...semnoxDefaultTheme, ...globalTheme, ...siteTheme, ...pageSpecificTheme };
        let mergedTheme = { ...globalTheme, ...siteTheme, ...pageSpecificTheme };

        // Convert the merged theme into a list of Tailwind CSS classes using the utility function
        return convertToTailwindClassList(mergedTheme);
    }

    /**
     * Sets the site-wide theme for the application.
     * 
     * @param siteTheme - Theme object containing site-specific styles
     */
    setSiteTheme(siteTheme: any) {
        this.siteTheme = siteTheme; // Store the site theme for later use in components
    }
}
