/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { Injectable, PLATFORM_ID, inject } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import {
    DEFAULT_APP_CONFIG_TOKEN,
    LanguageContainerServiceDL, SiteViewsServiceDL, ParafaitDefaultContainerService,
    areEqual,
    AuthenticateSystemUsersServiceDL,
    CookieService
} from 'lib-app-core';
import { AuthenticateSystemUsersDTOModel } from '../models/authenticate-system-users-dto.model';
import { AuthenticateSystemUsersServerServiceDL } from './data-layer/authenticate-system-users-server-dl.service';
import { isPlatformServer } from '@angular/common';
import { Router } from '@angular/router';


@Injectable({
    providedIn: 'root',
})
export abstract class AppInitBaseService {
    private _defaultAppConfig = inject(DEFAULT_APP_CONFIG_TOKEN);
    private _authenticateSystemUsersServerServiceDL = inject(AuthenticateSystemUsersServerServiceDL);
    private _authenticateSystemUsersServiceDL = inject(AuthenticateSystemUsersServiceDL);
    private _languageContainerServiceDL = inject(LanguageContainerServiceDL);
    private _siteViewsServiceDL = inject(SiteViewsServiceDL);
    private _parafaitDefaultContainerService = inject(ParafaitDefaultContainerService);
    private _platformId = inject(PLATFORM_ID);
    private _cookieService = inject(CookieService);
    private _router = inject(Router);

    private _webApiToken: string | null = null;

    protected _siteId: number = this._defaultAppConfig['siteId']; // Default site identifier
    protected langCode: number = this._defaultAppConfig['langCode'];
    protected machineName: number = this._defaultAppConfig['machineName'];

    protected abstract getAppSpecificApiData(): any;

    constructor() { }

    /**
     * Initialize site ID based on cookies or configuration
     */
    initializeSiteId(): number | null {
        const siteId = this._cookieService.getCookie('siteId');
        if (siteId && !isNaN(parseInt(siteId))) {
            this.siteId$ = parseInt(siteId);
            return this.siteId$;
        }
        
        // No site ID in cookies, check configuration
        const configSiteId = this._defaultAppConfig['siteId'];
        
        if (areEqual(configSiteId, -1)) { 
            // Multiple sites configured, need site selection
            return null;
        } else {
            // Single site configured
            this.siteId$ = configSiteId;
            return configSiteId;
        }
    }

    /**
     * Navigate to site selection page
     */
    navigateToSiteSelection(): void {
        this._router.navigate(['/site-selection']);
    }


    /**
     * Handle site initialization flow
     * 1. Initialize site ID
     * 2. Update site ID
     * 3. Load initial data
     * 4. Navigate to site selection page if no site ID is found
     */
    async handleSiteInitialization(): Promise<void> {
        const siteId = this.initializeSiteId();
        if (siteId) {
            await this.updateSiteId(siteId);
        } else {
            this.navigateToSiteSelection();
        }
    }
    
    async loadInitialData(siteId?: number) {
        if (siteId !== undefined) {
            this.siteId$ = siteId; // Update siteId dynamically
        }

        try {
            // Step 1: Authenticate user
            if (isPlatformServer(this._platformId))
                await this.authenticateUserServer();
            else
                await this.authenticateUser();
            // Step 2: Fetch required API data with new token
            await this.fetchApiData();
        } catch (error) {
            console.error('Failed to load initial data', error);
        }
    }

    private async authenticateUser() {
        const loginPayload = {
            machineName: this.machineName,
            languageCode: this.langCode,
            siteId: this.siteId$
        };

        this._authenticateSystemUsersServiceDL.buildApiParams(loginPayload);
        await firstValueFrom(this._authenticateSystemUsersServiceDL.load());

        this._authenticateSystemUsersServiceDL.subscribeToData((data: AuthenticateSystemUsersDTOModel) => {
            // console.log(data);
            this.webApiToken$ = data.WebApiToken;
        });
    }

    private async authenticateUserServer() {
        const loginPayload = {
            machineName: this.machineName,
            languageCode: this.langCode,
            siteId: this.siteId$,
            LoginId: process.env['LOGIN_ID'],
            Password: process.env['PASSWORD']
        };

        this._authenticateSystemUsersServerServiceDL.buildApiParams(loginPayload);
        await firstValueFrom(this._authenticateSystemUsersServerServiceDL.load());

        this._authenticateSystemUsersServerServiceDL.subscribeToData((data: AuthenticateSystemUsersDTOModel) => {
            this.webApiToken$ = data.WebApiToken;
        });
    }

    private async fetchApiData() {
        if (!this.webApiToken$) {
            throw new Error('Missing API token');
        }

        this._parafaitDefaultContainerService.buildApiParams({
            'siteId': this.siteId$
        });

        await Promise.all([
            firstValueFrom(this._languageContainerServiceDL.load()),
            firstValueFrom(this._siteViewsServiceDL.load()),
            firstValueFrom(this._parafaitDefaultContainerService.load()),
        ]);
    }

    async updateSiteId(newSiteId: number) {
        if (!areEqual(newSiteId, this.siteId$)) {
            this.siteId$ = newSiteId;

            // Set cookie for future visits with path '/' to ensure it's available across all routes
            this._cookieService.setCookie('siteId', newSiteId?.toString() ?? '', { path: '/' });
            await this.loadInitialData(newSiteId);
        }
    }

    set siteId$(value: number) {
        if (value)
            this._siteId = value;
    }

    get siteId$(): number {
        return this._siteId;
    }

    set webApiToken$(value: string | null) {
        if (value)
            this._webApiToken = value.trim();
    }

    get webApiToken$(): string | null {
        return this._webApiToken;
    }
}
