/**
 * @file AuthenticateSystemUsersService.ts
 * @description Service to authenticate system users by sending login credentials
 *              to a remote API.
 * <AUTHOR>
 * @date March 7, 2025
 */

export class AuthenticateSystemUsersService {
    private apiUrl: string = process.env['PARAFAIT_API_BASEURL'] || '';
    private loginId: string;
    private password: string;

    constructor() {
        // API endpoint for user authentication
        this.apiUrl = this.apiUrl + '/Login/AuthenticateSystemUsers';
        // console.log("Final API URL:", this.apiUrl);
        // console.log("env:", process.env); // ✅ Proper way to log objects


        // Hardcoded credentials (for production, use environment variables instead)
        this.loginId = process.env['LOGIN_ID'] || '';
        this.password = process.env['PASSWORD'] || '';
        // this.loginId = 'FNBWebsiteUser';
        // this.password = 'FnbWeB!1';
    }

    /**
     * Authenticates a system user by sending a POST request to the authentication API.
     * @param machineName - The name of the machine making the request.
     * @param languageCode - The language preference for the request.
     * @param siteId - The site identifier for authentication.
     * @returns A promise that resolves to the authentication response.
     * @throws Error if the request fails.
     */
    public async authenticate(machineName: string, languageCode: string, siteId: number): Promise<any> {
        const payload = {
            machineName,
            languageCode,
            siteId,
            LoginId: this.loginId,
            Password: this.password
        };

        const response = await fetch(this.apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        return response.json();
    }
}