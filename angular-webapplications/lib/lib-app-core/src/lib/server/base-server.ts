/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-03-06
 */
import { APP_BASE_HREF } from '@angular/common';
import { CommonEngine } from '@angular/ssr/node';
import express, { Express } from 'express';
import { fileURLToPath } from 'node:url';
import { dirname, join, resolve } from 'node:path';
import { AuthenticateSystemUsersController } from './authenticate-system-users.controller';

export abstract class BaseServer {
    protected app: Express;
    protected serverDistFolder: string;
    protected browserDistFolder: string;
    protected indexHtml: string;
    private commonEngine: CommonEngine;

    constructor(protected appName: string, protected bootstrap: any) {
        this.app = express();
        this.serverDistFolder = dirname(fileURLToPath(import.meta.url));
        this.browserDistFolder = resolve(this.serverDistFolder, '../browser');
        this.indexHtml = join(this.serverDistFolder, 'index.server.html');
        this.commonEngine = new CommonEngine();

        this.configureServer();
    }

    protected configureServer(): void {
        this.app.use(express.json()); // Ensure JSON parsing

        // Handle API routes BEFORE Angular SSR
        this.app.use('/api', (req, res, next) => {
            console.log(`API Request: ${req.originalUrl}`);
            next();
        });

        // Register API Route
		this.app.get('/api/test', (req, res) => {
			res.json({ waiver: 'This is test data.' });
		});
        
        new AuthenticateSystemUsersController(this.app);

        // Serve static files first
        this.app.use(express.static(this.browserDistFolder, { maxAge: '1y' }));

        // Angular SSR for non-API routes
        this.app.get('*', (req, res, next) => {
            if (req.path.startsWith('/api')) {
                return next(); // Don't process API requests with SSR
            }

            const { protocol, originalUrl, baseUrl, headers } = req;
            this.commonEngine
                .render({
                    bootstrap: this.bootstrap,
                    documentFilePath: this.indexHtml,
                    url: `${protocol}://${headers.host}${originalUrl}`,
                    publicPath: this.browserDistFolder,
                    providers: [{ provide: APP_BASE_HREF, useValue: baseUrl }],
                })
                .then((html) => res.send(html))
                .catch((err) => next(err));
        });
    }

    public start(port: number): void {
        this.app.listen(port, () => {
            console.log(`${this.appName} server running at http://localhost:${port}`);
        });
    }
}
