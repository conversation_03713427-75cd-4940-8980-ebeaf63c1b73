/**
 * @file AuthenticateSystemUsersController.ts
 * @description Controller for handling authentication requests and interacting with the authentication service.
 * <AUTHOR>
 * @date March 7, 2025
 */

import { Request, Response, Express } from 'express';
import { AuthenticateSystemUsersService } from './authenticate-system-users.service';

export class AuthenticateSystemUsersController {
    private authService: AuthenticateSystemUsersService;

    /**
     * Constructor initializes the authentication service and registers routes.
     * @param app - Express application instance.
     */
    constructor(app: Express) {
        this.authService = new AuthenticateSystemUsersService();
        this.registerRoutes(app);
    }

    /**
     * Registers the authentication API endpoint.
     * @param app - Express application instance.
     */
    private registerRoutes(app: Express): void {
        app.post('/api/authenticate-system-users', this.authenticateSystemUsers.bind(this));
    }

    /**
     * Handles authentication requests from clients.
     * @param req - Express request object.
     * @param res - Express response object.
     * @returns JSON response with authentication data or error message.
     */
    private async authenticateSystemUsers(req: Request, res: Response): Promise<Response> {
        try {
            const { machineName, languageCode, siteId } = req.body;
            
            // Validate request body parameters
            if (!machineName || !languageCode || siteId === undefined) {
                return res.status(400).json({ error: 'Missing required fields' });
            }
            
            // Call authentication service
            const data = await this.authService.authenticate(machineName, languageCode, siteId);
            return res.json(data);
        } catch (error) {
            console.error('Error authenticating system users:', error);
            return res.status(500).json({ error: 'Internal server error' });
        }
    }
}