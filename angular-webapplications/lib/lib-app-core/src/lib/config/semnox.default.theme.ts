/**
 * @fileoverview Default theme configuration
 * 
 * Note: The changes done to this file will have an impact on all the applications
 * Do not make any updates to this file without prior approval from the Team Lead/ Manager
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-10-07
 */

export interface ThemeObject {
    [key: string]: any;
}

// Global theme
export const SemnoxDefaultTheme: { [key: string]: ThemeObject } = {
    btn_primary: {
        font_weight: 'font-medium',
        text_align: 'text-center',
        font_color: 'text-white',
        bg_color: 'bg-primary-500',
        border_radius: 'rounded-lg',
        hover: { bg_color: 'bg-primary-800' },
        focus: {
            ring_size: 'ring-4',
            ring_color: 'ring-primary-300',
            outline: 'outline-none',
        },
    },
    btn_accent: {
        font_weight: 'font-medium',
        text_align: 'text-center',
        font_color: 'text-white',
        bg_color: 'bg-accent-500',
        border_radius: 'rounded-lg',
        hover: { bg_color: 'bg-primary-700' },
        focus: {
            ring_size: 'ring-4',
            ring_color: 'ring-primary-300',
            outline: 'outline-none',
        },
    },
    btn_primary_stroked: {
        font_weight: 'font-medium',
        text_align: 'text-center',
        font_color: 'text-primary-700',
        bg_color: 'bg-white',
        border_radius: 'rounded-lg',
        border: 'border',
        border_color: 'border-primary-700',
        // bgGradientToB: "bg-gradient-to-b",
        // fromPrimary500: "from-primary-500",
        // toAccent500: "to-accent-500",
        hover: {
            bg_color: 'bg-primary-700',
            font_color: 'text-white',
        },
        focus: {
            ring_size: 'ring-4',
            rinr_color: 'ring-primary-300',
            outline: 'outline-none'
        },
    },
    btn_accent_stroked: {
        font_weight: 'font-medium',
        text_align: 'text-center',
        font_color: 'text-primary-700',
        bg_color: 'bg-white',
        border_radius: 'rounded-lg',
        border: 'border',
        border_color: 'border-primary-700',
        hover: {
            bg_color: 'bg-primary-700',
            font_color: 'text-white',
        },
        focus: {
            ring_size: 'ring-4',
            rinr_color: 'ring-primary-300',
            outline: 'outline-none'
        },
    },
    input: {
        font_color: 'text-gray-800',
        bg_color: 'bg-white',
        border: 'border',
        border_color: 'border-gray-400',
        w: 'w-full',
        text: 'text-sm',
        px: 'px-4',
        py: 'py-2.5',
        rounded: 'rounded-xl',
        outline: 'outline-gray-600',
    },
    header: {
        header_wrapper: {
            "bg_color": "bg-white", //bg-[#1da1f2]
            "height": "py-6",
            "border": "border-b border-gray-200",
        },
        header_conatiner: {
            "container": 'container'
        },
        nav: {
            text: 'text-ink-500',
            textHover: 'hover:text-primary-500'
        }
    }
};