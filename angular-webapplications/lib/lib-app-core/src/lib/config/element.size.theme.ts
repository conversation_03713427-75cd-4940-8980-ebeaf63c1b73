/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-09-18
 */

export interface ThemeObject {
    [key: string]: any;
}

export const ElemSizeTheme: ThemeObject = {
    btn_xs: {
        px: 'px-3',
        py: 'py-2',
        font_size: 'text-xs'
    },
    btn_sm: {
        px: 'px-3',
        py: 'py-2',
        font_size: 'text-sm'
    },
    btn_base: {
        px: 'px-5',
        py: 'py-2.5',
        font_size: 'text-sm'
    },
    btn_lg: {
        px: 'px-5',
        py: 'py-3',
        font_size: 'text-base'
    },
    btn_xl: {
        px: 'px-6',
        py: 'py-3.5',
        font_size: 'text-base'
    }
}