/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
export const appCoreConstant = {
    SAMPLE: '',
    API_ENDPOINTS:{
        'NODE_AUTHENTICATE_SYSTEM_USERS': '/api/authenticate-system-users',
        'AUTHENTICATE_SYSTEM_USERS': '/Login/AuthenticateSystemUsers',
        'LANGUAGE_CONTAINER': '/Configuration/LanguageContainer',
        'SITE_VIEWS': '/Organization/SiteViews',
        'SITE_VIEWS_JSON': '/site-view.json',
        'PARAFAIT_DEFAULT_CONTAINER': '/Configuration/ParafaitDefaultContainer/Values'
    },
    IMAGES: {
        'PLACEHOLDER': 'assets/images/placeholder.png'
    }
};


/**
 * ================================================
 * USAGE
 * ================================================
 * use this utility function to get constants
 * 
 * Ex:
 * getConstantValue();
 * getConstantValue('SAMPLE');
 * getConstantValue('API_ENDPOINTS.AUTHENTICATE_SYSTEM_USERS');
 * ================================================
 */