<div class="bg-transparent md:bg-surface-white md:rounded-4xl h-full md:h-[calc(100vh-168px)]">
    <!-- Profile Title (Mobile Only) -->
    <h1 class="text-lg font-semibold text-primary mb-5 md:hidden">My Profile</h1>

    <!-- Profile Card -->
    <div class="h-full bg-surface-white shadow-lg p-6 mb-8 relative rounded-4xl">
        <!-- Title and Edit <PERSON> (Desktop) -->
        <div class="hidden md:flex items-center justify-between mb-6">
            <h1 class="text-lg font-semibold text-primary">My Profile</h1>
            @if (!isEditMode) {
            <button (click)="toggleEditMode()" class="text-secondary-blue hover:text-secondary-blue-dark">
                <img src="assets/icons/edit.svg" alt="Edit" />
            </button>
            }
        </div>

        <!-- <PERSON> (Mobile) -->
        @if (!isEditMode) {
        <button (click)="toggleEditMode()" class="absolute top-6 right-6 md:hidden">
            <img src="assets/icons/edit.svg" alt="Edit" />
        </button>
        }

        <!-- View Mode -->
        @if (!isEditMode) {
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-6">
            <div>
                <div class="text-xs text-neutral-dark mb-1">First Name</div>
                <div class="text-primary">{{ profile.firstName }}</div>
            </div>

            <div>
                <div class="text-xs text-neutral-dark mb-1">Last Name</div>
                <div class="text-primary">{{ profile.lastName }}</div>
            </div>

            <div>
                <div class="text-xs text-neutral-dark mb-1">Date of Birth</div>
                <div class="text-primary">{{ profile.dateOfBirth }}</div>
            </div>

            <div>
                <div class="text-xs text-neutral-dark mb-1">Email Address</div>
                <div class="text-primary">{{ profile.email }}</div>
            </div>

            <div>
                <div class="text-xs text-neutral-dark mb-1">Mobile Number</div>
                <div class="text-primary">
                    {{ profile.countryCode }} {{ profile.mobileNumber }}
                </div>
            </div>
        </div>
        }

        <!-- Edit Mode -->
        @if (isEditMode) {
        <form [formGroup]="profileForm" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- First Name -->
            <div>
                <lib-text-input formControlName="firstName" name="firstName" label="First Name" [required]="true"
                    [errorMessage]="hasError('firstName')"></lib-text-input>
            </div>

            <!-- Last Name -->
            <div>
                <lib-text-input formControlName="lastName" name="lastName" label="Last Name" [required]="true"
                    [errorMessage]="hasError('lastName')"></lib-text-input>
            </div>

            <!-- Date of Birth -->
            <div>
                <lib-date-picker formControlName="dateOfBirth" name="dateOfBirth" label="Date of Birth"
                    [placeholder]="dateFormat.toUpperCase()" [format]="dateFormat" [required]="true" [rightOffset]="0"
                    [errorMessage]="hasError('dateOfBirth')"></lib-date-picker>
            </div>

            <!-- Email Address -->
            <div>
                <lib-text-input formControlName="email" name="email" label="Email Address" type="email"
                    [required]="true" [errorMessage]="hasError('email')"></lib-text-input>
            </div>

            <!-- Mobile Number -->
            <div>
                <lib-phone-input formControlName="mobileNumber" name="mobileNumber" label="Mobile Number"
                    [required]="true" [errorMessage]="hasError('mobileNumber')"></lib-phone-input>
            </div>

            <!-- Action Buttons -->
            <div class="col-span-full mt-4 pt-4 border-t border-neutral-light">
                <div class="flex flex-col sm:flex-row gap-4 lg:w-[60%]">
                    <button type="button" (click)="updateProfile()" [disabled]="profileForm.invalid"
                        class="w-full py-3 px-4 bg-primary text-white font-medium rounded-full disabled:bg-surface">
                        Update
                    </button>
                    <button type="button" (click)="cancelEdit()"
                        class="w-full py-3 px-4 bg-surface-white text-black border border-primary font-medium rounded-full">
                        Cancel
                    </button>
                </div>
            </div>
        </form>
        }
    </div>
</div>