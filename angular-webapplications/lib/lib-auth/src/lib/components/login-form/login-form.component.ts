import { CommonModule } from '@angular/common';
import { Component, signal, TemplateRef } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ErrorMessage, ForgotPasswordComponent } from '../forgot-password/forgot-password.component';
import { ModalComponent, PageFooterComponent, TextInputComponent } from 'lib-ui-kit';
import { STRONG_PASSWORD_REGEX } from '../registration-form-base/registration-form-base.component';

interface LoginFormData {
    email: FormControl<string | null>;
    password: FormControl<string | null>;
}
export type ErrorMessagLogin<T> = {
    [K in keyof T]?: Record<string, string | TemplateRef<Component> | null>;
};

@Component({
    selector: 'lib-login-form',
    imports: [
        ReactiveFormsModule,
        CommonModule,
        ForgotPasswordComponent,
        TextInputComponent,
        ModalComponent,
        <PERSON>F<PERSON>erComponent
    ],
    standalone: true,
    templateUrl: './login-form.component.html',
    styleUrl: './login-form.component.css'
})
export class LoginFormComponent {
    loginForm: FormGroup;
    readonly showForgotPassword = signal<boolean>(false);

    errorMessages: ErrorMessage<LoginFormData> = {
        email: {
            required: 'Email address is required',
            email: 'Please enter a valid email address',
        },
        password: {
            required: 'Password is required',
            pattern:
                'Atleast 8 characters, 1 uppercase letter, 1 lowercase letter, 1 number and 1 special character',
        },
    };

    constructor(private fb: FormBuilder) {
        this.loginForm = this.fb.group({
            email: ['', [Validators.required, Validators.email]],
            password: [
                '',
                [Validators.required, Validators.pattern(STRONG_PASSWORD_REGEX)],
            ],
        });
    }

    closeForgotPassword() {
        this.showForgotPassword.set(false);
    }

    hasError(
        fieldName: keyof LoginFormData
    ): string | null | TemplateRef<Component> {
        const control = this.loginForm.get(fieldName);

        if (control?.invalid && control?.touched && control.errors) {
            const errorKeys = Object.keys(control.errors);
            if (errorKeys.length > 0) {
                const firstErrorKey = errorKeys[0];
                return this.errorMessages[fieldName]?.[firstErrorKey] ?? null;
            }
        }
        return null;
    }

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onSubmit() { }
}
