<div class="flex flex-col gap-5">
    <p class="text-neutral-dark text-sm">
        Add the below details to get an account. We don't share or sell your data.
    </p>

    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
        <div class="flex flex-col space-y-4 max-w-[23rem]">
            <!-- Email Address -->
            <lib-text-input formControlName="email" label="Email Address" type="email" placeholder="Enter email address"
                [required]="true" [errorMessage]="hasError('email')"></lib-text-input>

            <!-- Password -->
            <lib-text-input formControlName="password" label="Password" type="password" placeholder="Enter password"
                [required]="true" [showPasswordToggle]="true" [errorMessage]="hasError('password')"></lib-text-input>

            <!-- Forgot Password  -->
            <button class="text-blue-500 underline self-start" (click)="showForgotPassword.set(true)">
                Forgot password?
            </button>
        </div>

        <!-- Desktop CTA -->
        <div class="hidden md:block">
            <hr class="my-4 text-surface" />

            <div class="md:flex flex-col md:flex-row md:items-center gap-4 hidden">
                <!-- Login Button -->
                <button [disabled]="loginForm.invalid" type="submit"
                    class="w-full max-w-[23rem] py-3 px-4 bg-primary text-white font-medium rounded-4xl disabled:bg-surface">
                    Login
                </button>
                <p class="text-primary text-sm text-center">
                    Don't have an account?
                    <a href="#" class="text-secondary-blue underline">Sign up</a>
                </p>
            </div>
        </div>
    </form>
</div>

<!-- Footer CTA (Mobile) -->
<lib-page-footer>
    <div class="flex flex-col md:flex-row items-center gap-4">
        <!-- Login Button -->
        <button [disabled]="loginForm.invalid" type="submit"
            class="w-full max-w-[23rem] mx-auto py-3 px-4 bg-primary text-white font-medium rounded-4xl disabled:bg-surface">
            Login
        </button>
        <p class="text-primary text-sm text-center">
            Don't have an account?
            <a href="#" class="text-secondary-blue underline">Sign up</a>
        </p>
    </div>
</lib-page-footer>

<!-- Forgot Password Modal -->
<ng-template #forgotPasswordContent>
    <lib-forgot-password />
</ng-template>

<lib-modal [isOpen]="showForgotPassword()" [modalContent]="forgotPasswordContent" (closeModal)="closeForgotPassword()">
</lib-modal>