import { CommonModule } from '@angular/common';
import { Component, TemplateRef, viewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { TextInputComponent } from 'lib-ui-kit';

interface ResetPasswordFormData {
    email: FormControl<string | null>;
};
export type ErrorMessage<T> = {
    [K in keyof T]?: Record<string, string | TemplateRef<Component> | null>;
};

@Component({
    selector: 'lib-forgot-password',
    standalone: true,
    imports: [ReactiveFormsModule, CommonModule, TextInputComponent],
    templateUrl: './forgot-password.component.html',
    styleUrl: './forgot-password.component.css'
})
export class ForgotPasswordComponent {
    resetPasswordForm: FormGroup;

    userNotfoundTemplate = viewChild<TemplateRef<Component>>(
        'userNotfoundTemplate'
    );

    // Getter ensures safe access to TemplateRef-based errors, which are only available after view init.
    get errorMessages(): ErrorMessage<ResetPasswordFormData> {
        return {
            email: {
                required: 'Email address is required',
                email: 'Please enter a valid email address',
                userNotfound: this.userNotfoundTemplate() ?? null,
            },
        };
    }

    constructor(private fb: FormBuilder) {
        this.resetPasswordForm = this.fb.group({
            email: ['', [Validators.required, Validators.email]],
        });
    }

    hasError(
        fieldName: keyof ResetPasswordFormData
    ): string | null | TemplateRef<Component> {
        const control = this.resetPasswordForm.get(fieldName);

        if (control?.invalid && control?.touched && control.errors) {
            const errorKeys = Object.keys(control.errors);
            if (errorKeys.length > 0) {
                const firstErrorKey = errorKeys[0];
                return this.errorMessages[fieldName]?.[firstErrorKey] ?? null;
            }
        }
        return null;
    }

    userValidator() {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const email = this.resetPasswordForm.get('email')?.value;
        // Call necessary function here to check the user existence in the database
        const user = false;
        if (!user) {
            this.resetPasswordForm.get('email')?.setErrors({ userNotfound: true });
            return { userNotfound: true };
        }

        return null;
    }

    onSubmit() {
        const userError = this.userValidator();
        if (!userError) {
            // Proceed with reset logic
        }
    }
}
