<div class="p-5 grid gap-5">
    <div class="flex flex-col items-center">
        <div class="mb-2">
            <img src="assets/icons/forgot-password.svg" alt="Forgot Password" class="w-12 h-12" />
        </div>
        <h1 class="text-xl font-semibold text-center">Forgot password</h1>
    </div>

    <form [formGroup]="resetPasswordForm" (ngSubmit)="onSubmit()">
        <div class="flex flex-col gap-5">
            <div class="flex flex-col gap-1 items-center justify-center text-sm">
                <span class="font-medium">Identify yourself</span>
                <p class="w-full text-primary text-center">
                    Please enter the registered email address to receive link to reset
                </p>
            </div>

            <lib-text-input formControlName="email" label="Email Address" type="email" placeholder="Enter email address"
                [required]="true" [errorMessage]="hasError('email')"></lib-text-input>

            <button [disabled]="resetPasswordForm.invalid"
                class="w-full py-3 px-4 bg-primary text-white font-medium rounded-4xl border-0 disabled:bg-surface">
                Submit
            </button>
        </div>
    </form>
</div>

<ng-template #userNotfoundTemplate>
    The email ID doesn't exist in our system.<br />
    <a class="text-secondary-blue" href="/">Sign up</a> to get a new account.
</ng-template>