<div>
    <p class="text-sm text-neutral-dark mb-6">
        Add the below details to get an account. We don't share or sell your data.
    </p>

    <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
        <fieldset class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <!-- First Name -->
            <lib-text-input formControlName="firstName" label="First Name" placeholder="Enter first name"
                [required]="true" [errorMessage]="hasError('firstName')"></lib-text-input>

            <!-- Last Name -->
            <lib-text-input formControlName="lastName" label="Last Name" placeholder="Enter last name" [required]="true"
                [errorMessage]="hasError('lastName')"></lib-text-input>

            <!-- Date of Birth -->
            <lib-date-picker formControlName="dateOfBirth" label="Date of Birth"
                [placeholder]="dateFormat.toUpperCase()" [format]="dateFormat" [required]="true" [rightOffset]="0"
                [errorMessage]="hasError('dateOfBirth')" [rightOffset]="0" [minYear]="1900"></lib-date-picker>
        </fieldset>

        <fieldset class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <!-- Email Address -->
            <lib-text-input formControlName="email" label="Email Address" type="email" placeholder="Enter email address"
                [required]="true" [errorMessage]="hasError('email')"></lib-text-input>

            <!-- Mobile Number -->
            <lib-phone-input formControlName="mobileNumber" label="Mobile Number" placeholder="Enter mobile number"
                [required]="true" [errorMessage]="hasError('mobileNumber')"></lib-phone-input>
        </fieldset>

        <fieldset class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <!-- Password -->
            <lib-text-input formControlName="password" label="Password" type="password" placeholder="Enter password"
                [required]="true" [showPasswordToggle]="true" [description]="fieldDescription.password ?? null"
                [errorMessage]="hasError('password')"></lib-text-input>

            <!-- Confirm Password -->
            <lib-text-input formControlName="confirmPassword" label="Confirm Password" type="password"
                placeholder="Re-enter password" [required]="true" [showPasswordToggle]="true"
                [errorMessage]="hasError('confirmPassword')"></lib-text-input>
        </fieldset>

        <!-- Promotion Card -->
        <div class="mb-6 max-w-xs p-2 rounded-3xl bg-surface-lightest">
            <div class="relative overflow-hidden rounded-xl w-full">
                <img src="assets/images/opt-promotion.jpg" alt="Promotion" class="w-full h-36 object-cover scale-125" />
                <div class="absolute inset-0 flex items-end p-5 text-surface-white bg-gradient-to-top-black">
                    <h3 class="text-xl font-medium max-w-[80%]">
                        Get 50% off on your next party booking
                    </h3>
                </div>
            </div>

            <!-- Opt-in Checkbox -->
            <div class="flex items-center justify-between space-x-2 px-2 mt-2">
                <lib-checkbox id="promotion" [name]="'promotion'" [label]="'Opt in for promotion'"
                    [formControlName]="'optInPromotion'" customClass="w-[18px] h-[18px]" />
                <a href="#" class="text-sm text-blue-600 underline">View Benefits</a>
            </div>
        </div>

        <!-- Minors -->
        <ng-content select="[add-minor-form]"></ng-content>

        <!-- Register Button -->
        <hr class="my-4 text-surface hidden md:block" />

        <!-- Desktop CTA -->
        <div class="md:flex flex-col md:flex-row md:items-center gap-4 hidden">
            <!-- Login Button -->
            <button [disabled]="!registerForm.invalid" type="submit"
                class="w-full max-w-[23rem] py-3 px-4 bg-primary text-white font-medium rounded-4xl disabled:bg-surface">
                Register and sign waiver
            </button>
            <div class="text-sm text-center md:text-right">
                Already have an account?
                <a href="#" class="text-blue-600 underline">Login</a>
            </div>
        </div>
    </form>
</div>

<!-- Footer CTA (Mobile) -->
<lib-page-footer>
    <div class="flex flex-col md:flex-row items-center gap-4">
        <!-- Login Button -->
        <button [disabled]="!registerForm.invalid" (click)="onSubmit()" type="submit"
            class="w-full max-w-[23rem] py-3 px-4 bg-primary text-white font-medium rounded-4xl disabled:bg-surface">
            Register and sign waiver
        </button>
        <div class="text-sm text-center md:text-right">
            Already have an account?
            <a href="#" class="text-blue-600 underline">Login</a>
        </div>
    </div>
</lib-page-footer>