import { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';
import { Component, effect, ElementRef, HostListener, Inject, Injector, Input, input, output, PLATFORM_ID, Renderer2, runInInjectionContext, signal, TemplateRef, viewChild } from '@angular/core';
import { ClickOutsideDirective } from '../../directives/click-outside.directive';
import { FocusTrapService } from '../../services/focus-trap/focus-trap.service';

@Component({
    selector: 'lib-modal',
    imports: [CommonModule, ClickOutsideDirective],
    templateUrl: './modal.component.html',
    styleUrl: './modal.component.css'
})
export class ModalComponent {
    @Input() modalContext: any;


    modalContent = input<TemplateRef<Component> | null>(null);
    isOpen = input.required<boolean>();
    closeModal = output<void>();
    dialogueHeader = input<string>('');
    readonly isClosing = signal(false);

    // Reference to modal container
    modalContainer = viewChild<ElementRef>('modalContainer');

    // Reference to popup mask
    popupMask = viewChild<ElementRef>('popupMask');

    // Store first and last focusable elements
    private firstFocusableElement: HTMLElement | null = null;
    private lastFocusableElement: HTMLElement | null = null;

    constructor(
        private renderer: Renderer2,
        private injector: Injector,
        private focusTrapService: FocusTrapService,
        @Inject(PLATFORM_ID) private platformId: object,
        @Inject(DOCUMENT) private document: Document
    ) { }

    // On pressing escape close the modal
    @HostListener('document:keydown.escape')
    onEscapePress() {
        if (isPlatformBrowser(this.platformId) && this.isOpen()) {
            this.close();
        }
    }

    @HostListener('document:keydown', ['$event'])
    handleKeydown(event: KeyboardEvent) {
        if (
            isPlatformBrowser(this.platformId) &&
            this.isOpen() &&
            event.key === 'Tab'
        ) {
            // If there are no focusable elements, just prevent tabbing
            if (!this.firstFocusableElement || !this.lastFocusableElement) {
                event.preventDefault();
                return;
            }

            // If shift + tab pressed and focus is on first element, move to last element
            if (
                event.shiftKey &&
                this.document.activeElement === this.firstFocusableElement
            ) {
                event.preventDefault();
                this.lastFocusableElement.focus();
            }
            // If tab pressed and focus is on last element, move to first element
            else if (
                !event.shiftKey &&
                this.document.activeElement === this.lastFocusableElement
            ) {
                event.preventDefault();
                this.firstFocusableElement.focus();
            }
        }
    }

    ngAfterViewInit() {
        // Watch for isOpen changes using effect
        if (isPlatformBrowser(this.platformId)) {
            runInInjectionContext(this.injector, () => {
                effect(() => {
                    const isOpenValue = this.isOpen();
                    if (isOpenValue) {
                        // Add 'active' class after a short delay
                        setTimeout(() => {
                            const popupMaskElement = this.popupMask(); // Access the signal's value
                            if (popupMaskElement && popupMaskElement.nativeElement) {
                                this.renderer.addClass(
                                    popupMaskElement.nativeElement,
                                    'active'
                                );
                            }
                        }, 0); // Small delay to allow the element to be rendered

                        const container = this.modalContainer()?.nativeElement;
                        if (container) {
                            this.focusTrapService.trapFocus(container, this.renderer);
                        }
                    } else {
                        // Remove 'active' class before closing
                        const popupMaskElement = this.popupMask(); // Access the signal's value

                        if (popupMaskElement && popupMaskElement.nativeElement) {
                            this.renderer.removeClass(
                                popupMaskElement.nativeElement,
                                'active'
                            );
                        }

                        this.focusTrapService.releaseFocus(this.renderer);
                    }
                });
            });

            // Initial setup if modal is open on init
            if (this.isOpen()) {
                const container = this.modalContainer()?.nativeElement;
                if (container) {
                    this.focusTrapService.trapFocus(container, this.renderer);
                }
            }
        }
    }

    close() {
        if (isPlatformBrowser(this.platformId)) {
            this.isClosing.set(true); // Set isClosing to true before removing the modal

            setTimeout(() => {
                const popupMaskElement = this.popupMask(); // Access the signal's value
                if (popupMaskElement && popupMaskElement.nativeElement) {
                    this.renderer.removeClass(popupMaskElement.nativeElement, 'active');
                }

                this.closeModal.emit(); // Emit close event after the animation delay

                this.focusTrapService.allowBackgroundScrolling();
                this.focusTrapService.releaseFocus(this.renderer);

                this.isClosing.set(false); // Reset isClosing after closing animation
            }, 200); // Adjust delay based on your CSS animation duration
        }
    }
}
