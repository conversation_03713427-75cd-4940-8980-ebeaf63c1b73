.popup-mask {
    background-color: var(--color-modal-overlay-background);
    transition: opacity 0.2s ease-in-out;
    /* Fade in/out the background mask */
    opacity: 0;
    /* Initially transparent */
}

.popup-mask.active {
    opacity: 1;
    /* Fully opaque when active */
}

dialog[open] {
    animation: fadeIn 0.3s ease-in-out;
}

dialog.closing {
    animation: fadeOut 0.2s ease-in-out forwards;
    opacity: 0;
}

body.modal-open {
    overflow: hidden;
    /* Prevent background scrolling when modal is open */
}

/* Additional optional animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        scale: 0.8;
    }

    to {
        opacity: 1;
        scale: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }

    to {
        opacity: 0;
        transform: scale(0.8);
    }
}