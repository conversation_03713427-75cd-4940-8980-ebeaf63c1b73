// Add more country codes as needed, eg IN for India
export type CountryCode = 'US' | 'IN';

export interface Country {
  code: CountryCode;
  name: string;
  dialCode: string;
  numericPattern: RegExp;
  countryFlagIcon: string;
  examples: string[];
  allowedCharacters: RegExp;
}

// You can add more countries to this list as needed
export const Countries: Country[] = [
  {
    code: 'US',
    name: 'United States',
    dialCode: '+1',
    countryFlagIcon: '/assets/flags/us.png',
    numericPattern:
      /^\(?([2-9][0-8][0-9])\)?[-.\s]?([2-9][0-9]{2})[-.\s]?([0-9]{4})$/,
    examples: ['************', '(*************', '************', '2125551234'],
    allowedCharacters: /[\d().\-\s]/, // Used for single character validation on keypress events, Because checking against numericPattern will fail for the single character
  },

  {
    code: 'IN',
    name: 'India',
    dialCode: '+91',
    countryFlagIcon: '/assets/flags/in.png',
    numericPattern: /^[6-9]\d{9}$/,
    examples: ['9876543210', '9123456789'],
    allowedCharacters: /[\d\s]/, // Allows digits and spaces for Indian phone numbers
  },
];
