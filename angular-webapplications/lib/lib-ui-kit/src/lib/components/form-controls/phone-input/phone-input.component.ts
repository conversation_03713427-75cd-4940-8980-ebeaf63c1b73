import { Component, forwardRef, input, OnInit, TemplateRef } from '@angular/core';
import { ControlValueAccessor, FormControl, NG_VALUE_ACCESSOR, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ClickOutsideDirective } from '../../../directives/click-outside.directive';
import { Countries, Country } from '../phone-input.constants';
import { Subscription } from 'rxjs';
import { isTemplateRef } from '../../../utils';


@Component({
    selector: 'lib-phone-input',
    imports: [CommonModule, ReactiveFormsModule, ClickOutsideDirective],
    templateUrl: './phone-input.component.html',
    styleUrl: './phone-input.component.css',
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => PhoneInputComponent),
            multi: true,
        },
    ],
})
export class PhoneInputComponent implements ControlValueAccessor, OnInit {
    label = input('');
    placeholder = input('');
    required = input(false);
    disabled = input(false);

    countries = input<Country[]>(Countries);
    errorMessage = input<string | null | TemplateRef<Component>>(null);

    selectedCountry: Country = this.countries()[0];
    isDropdownOpen = false;
    value: string | null = '';
    control = new FormControl('', [
        Validators.pattern(this.selectedCountry.numericPattern),
    ]);
    isTemplateRef = isTemplateRef;
    private valueChangesSubscription: Subscription | null = null;

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private onChange: (value: string | null) => void = () => { };
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private onTouched: () => void = () => { };

    ngOnInit(): void {
        this.valueChangesSubscription = this.control.valueChanges.subscribe(
            value => {
                this.value = value;
                this.onChange(value);
                this.validatePhoneNumber(value);
            }
        );

        if (this.countries().length > 0) {
            this.selectedCountry = this.countries()[0];
            this.updatePattern();
        }
    }

    ngOnDestroy(): void {
        if (this.valueChangesSubscription) {
            this.valueChangesSubscription.unsubscribe();
        }
    }

    private validatePhoneNumber(value: string | null): void {
        if (!value) return;

        const pattern = this.selectedCountry.numericPattern;
        if (
            !pattern.test(value.trim()) &&
            this.control.touched &&
            this.control.dirty
        ) {
            this.control.setErrors({ pattern: true });
        } else {
            this.control.setErrors(null);
        }
    }

    private updatePattern(): void {
        this.control.setValidators([
            Validators.pattern(this.selectedCountry.numericPattern),
        ]);
        this.control.updateValueAndValidity();

        // Revalidate current value with new pattern
        if (this.control.value) {
            this.validatePhoneNumber(this.control.value);
        }
    }

    selectCountry(country: Country): void {
        this.selectedCountry = country;
        this.isDropdownOpen = false;
        this.updatePattern();
    }

    writeValue(value: string | null): void {
        this.value = value;
        this.control.setValue(value, { emitEvent: false });
    }

    registerOnChange(fn: (value: string | null) => void): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: () => void): void {
        this.onTouched = fn;
    }

    setDisabledState(isDisabled: boolean): void {
        if (isDisabled) {
            this.control.disable();
        } else {
            this.control.enable();
        }
    }

    onBlur(): void {
        this.onTouched();
        this.validatePhoneNumber(this.control.value);
    }

    toggleDropdown(): void {
        this.isDropdownOpen = !this.isDropdownOpen;
    }

    hasError(): boolean {
        return (
            !!this.errorMessage() || (this.control.invalid && this.control.touched)
        );
    }

    getFormatExamples(): string {
        return this.selectedCountry.examples.join(', ');
    }

    onKeyPress(event: KeyboardEvent): void {
        const allowedCharacters = this.selectedCountry.allowedCharacters;
        if (!allowedCharacters.test(event.key)) {
            event.preventDefault();
        }
    }
}
