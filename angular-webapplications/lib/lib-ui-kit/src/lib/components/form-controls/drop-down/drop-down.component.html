<div class="relative w-full" libClickOutside (clickOutside)="dropdownOpen.set(false)">
    <label class="block text-xs font-medium text-primary mb-2" [for]="control">
        {{ label() }}
    </label>
    <div class="w-full cursor-pointer text-sm py-3.5 px-4 border border-surface bg-surface-white rounded-xl focus:outline-none focus-within:ring-1 focus-within:ring-secondary-blue placeholder:text-neutral-dark"
        [class.disabled]="disabled()" (click)="toggleDropdown(); $event.stopPropagation()"
        (keydown)="toggleDropdown(); $event.stopPropagation()" tabindex="0" [ngClass]="{
      'ring-1 ring-secondary-blue': dropdownOpen(),
      'border-none ring-1 ring-feedback-error': hasError(),
    }">
        <button class="flex items-center justify-between w-full rounded truncate gap-2"
            (click)="toggleDropdown(); $event.stopPropagation()" aria-label="Select an option" (blur)="onBlur()">
            <span class="text-sm truncate" [ngClass]="{
          'text-primary': selectedOption()?.label,
          'text-neutral-dark': !selectedOption()?.label,
        }">
                {{ selectedOption()?.label || placeholder() }}
            </span>
            <img src="assets/icons/chevron-down.svg" alt="Dropdown" />
        </button>

        @if (dropdownOpen()) {
        <div class="absolute right-0 mt-4 bg-surface-white rounded-xl shadow-sm z-10 w-max overflow-y-auto" [ngClass]="{
          '-top-52 mt-5 left-0.5  max-h-48': position() === 'top',
          'max-h-36': position() === 'bottom',
        }">
            <div class="p-1 grid gap-1">
                @for (option of options(); track $index) {
                <button (click)="selectOption(option); $event.stopPropagation()"
                    class="block w-full text-left px-4 py-2 text-sm text-primary hover:bg-surface-lightest" [ngClass]="{
                'bg-surface-lightest': selectedOption()?.value === option.value,
                'font-medium text-secondary-blue':
                  selectedOption()?.value === option.value,
              }">
                    {{ option.label }}
                </button>
                }
            </div>
        </div>
        }
    </div>
    @if (hasError()) {
    <div class="text-feedback-error text-xs mt-2">
        @if (isTemplateRef(errorMessage())) {
        <ng-container *ngTemplateOutlet="$any(errorMessage())"></ng-container>
        } @else {
        {{ errorMessage() }}
        }
    </div>
    }
</div>