import { Component, input, signal, TemplateRef } from '@angular/core';
import { ControlValueAccessor, FormControl, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ClickOutsideDirective } from '../../../directives/click-outside.directive';
import { isTemplateRef } from '../../../utils';
export interface DropdownOption {
    label: string;
    value: string;
}
@Component({
    selector: 'lib-drop-down',
    imports: [CommonModule, ReactiveFormsModule, ClickOutsideDirective],
    templateUrl: './drop-down.component.html',
    styleUrl: './drop-down.component.css'
})
export class DropDownComponent implements ControlValueAccessor {
    label = input<string | null>(null);
    name = input<string | null>(null);
    placeholder = input<string | null>('Choose an option');
    disabled = input<boolean>(false);
    options = input<DropdownOption[]>([]);
    errorMessage = input<string | null | TemplateRef<Component>>(null);
    position = input<'top' | 'bottom'>('bottom');
    readonly dropdownOpen = signal<boolean>(false);
    readonly selectedOption = signal<DropdownOption | null>(null);
    required = input<boolean>(false);
    control = new FormControl('');
    isTemplateRef = isTemplateRef;

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private onChange: (value: string | null) => void = () => { };
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private onTouched: () => void = () => { };

    toggleDropdown() {
        this.dropdownOpen.set(!this.dropdownOpen());
    }

    selectOption(option: DropdownOption) {
        this.selectedOption.set(option);
        this.onChange(option.value);
        this.dropdownOpen.set(false);
    }

    writeValue(value: string | null): void {
        const selected = this.options().find(opt => opt.value === value) || null;
        this.selectedOption.set(selected);
    }

    registerOnChange(fn: (value: string | null) => void): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: () => void): void {
        this.onTouched = fn;
    }

    onBlur(): void {
        this.onTouched();
    }

    setDisabledState(isDisabled: boolean): void {
        if (isDisabled) {
            this.control.disable();
        } else {
            this.control.enable();
        }
    }

    hasError(): boolean {
        if (this.dropdownOpen()) {
            return false;
        }

        return (
            !!this.errorMessage() || (this.control.invalid && this.control.touched)
        );
    }
}

