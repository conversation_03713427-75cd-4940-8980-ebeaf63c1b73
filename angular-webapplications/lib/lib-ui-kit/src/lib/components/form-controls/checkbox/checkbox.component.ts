import { Component, forwardRef, input, On<PERSON><PERSON><PERSON>, OnInit, TemplateRef } from '@angular/core';
import { ControlValueAccessor, FormControl, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { CommonModule } from '@angular/common';
import { isTemplateRef } from '../../../utils';

@Component({
    selector: 'lib-checkbox',
    imports: [CommonModule, ReactiveFormsModule],
    templateUrl: './checkbox.component.html',
    styleUrl: './checkbox.component.css',
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => CheckBoxComponent),
            multi: true,
        },
    ]
})
export class CheckBoxComponent
    implements ControlValueAccessor, OnInit, OnDestroy {
    name = input<string>('');
    label = input<string>('');
    required = input<boolean>(false);
    disabled = input<boolean>(false);
    errorMessage = input<string | null | TemplateRef<Component>>(null);
    customClass = input<string | null>(null);
    checked = false;
    control = new FormControl(false);
    isTemplateRef = isTemplateRef;
    private valueChangesSubscription: Subscription | null = null;

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private onChange: (value: boolean) => void = () => { };
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private onTouched: () => void = () => { };

    ngOnInit(): void {
        this.setDisabledState(this.disabled());

        this.valueChangesSubscription = this.control.valueChanges.subscribe(
            value => {
                this.checked = !!value;
                this.onChange(this.checked);
            }
        );
    }

    ngOnDestroy(): void {
        if (this.valueChangesSubscription) {
            this.valueChangesSubscription.unsubscribe();
        }
    }

    writeValue(value: boolean): void {
        this.checked = !!value;
        this.control.setValue(this.checked, { emitEvent: false });
    }

    registerOnChange(fn: (value: boolean) => void): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: () => void): void {
        this.onTouched = fn;
    }

    setDisabledState(isDisabled: boolean): void {
        if (isDisabled) {
            this.control.disable();
        } else {
            this.control.enable();
        }
    }

    onBlur(): void {
        this.onTouched();
    }

    hasError(): boolean {
        return (
            !!this.errorMessage() || (this.control.invalid && this.control.touched)
        );
    }

    dynamicClass(): string {
        let className = '';
        if (this.hasError()) {
            className = 'border-none ring-1 ring-feedback-error';
        }
        if (this.customClass()) {
            className += ` ${this.customClass()}`;
        }
        return className;
    }
}


