<div class="flex flex-col items-start gap-2">
    <div class="flex items-center justify-center gap-2">
        <input type="checkbox" [id]="name()" [name]="name()" [formControl]="control" [required]="required()"
            (blur)="onBlur()"
            class="appearance-none rounded-[4px] border-2 border-secondary-blue bg-surface-white flex items-center justify-center cursor-pointer checked:bg-secondary-blue checked:bg-[url('/assets/icons/checkbox-checked.svg')] checked:bg-cover bg-no-repeat bg-center disabled:border-surface disabled:cursor-not-allowed"
            [ngClass]="dynamicClass()" />
        @if (label()) {
        <label class="block text-sm text-primary" [for]="name()">
            {{ label() }}
        </label>
        }
    </div>
    @if (hasError()) {
    <div class="text-feedback-error text-xs">
        @if (isTemplateRef(errorMessage())) {
        <ng-container *ngTemplateOutlet="$any(errorMessage())"></ng-container>
        } @else {
        <span>{{ errorMessage() }}</span>
        }
    </div>
    }
</div>