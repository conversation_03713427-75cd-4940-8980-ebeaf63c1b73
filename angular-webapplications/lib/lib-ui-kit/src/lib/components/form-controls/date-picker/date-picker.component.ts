import { Component, ElementRef, forwardRef, Inject, input, OnDestroy, OnInit, output, TemplateRef, viewChild } from '@angular/core';
import { ControlValueAccessor, FormControl, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import {
    eachDayOfInterval,
    endOfMonth,
    format,
    getMonth,
    getYear,
    isToday,
    isValid,
    parse,
    startOfMonth,
} from 'date-fns';
import { ClickOutsideDirective } from '../../../directives/click-outside.directive';
import { isTemplateRef } from '../../../utils';

interface CalendarDay {
    date: Date;
    isCurrentMonth: boolean;
    isSelected: boolean;
    isToday: boolean;
}

export const DATE_FORMAT = 'dd-MM-yyyy';
export const CALENDAR_GRID_COLUMNS = 7;
export const CALENDAR_GRID_ROWS = 6;
export const YEARS_OFFSET = 100;



@Component({
    selector: 'lib-date-picker',
    imports: [CommonModule, ReactiveFormsModule, ClickOutsideDirective],
    templateUrl: './date-picker.component.html',
    styleUrl: './date-picker.component.css',
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => DatePickerComponent),
            multi: true,
        }
    ]
})
export class DatePickerComponent implements ControlValueAccessor, OnInit, OnDestroy {
    label = input<string>('');
    name = input<string>('');
    placeholder = input<string>('');
    required = input<boolean>(false);
    disabled = input<boolean>(false);
    format = input<string>(DATE_FORMAT);
    errorMessage = input<string | null | TemplateRef<Component>>(null);
    minDate = input<Date | null>(null);
    maxDate = input<Date | null>(null);
    minYear = input<number>(new Date().getFullYear() - YEARS_OFFSET);
    maxYear = input<number>(new Date().getFullYear());
    position = input<'top' | 'bottom'>('bottom');
    rightOffset = input<number | null>(null);

    // When this enabled, the datepicker font size is reduced to 10px when width is less than 390px
    useResponsiveFontSize = input<boolean>(false);

    // When this enabled, the datepicker cell dimensions are reduced to 1.75rem when height is less than 720px
    useResponsiveHeight = input<boolean>(false);
    dateSelected = output<Date | null>();

    calendarContainer = viewChild('calendarContainer');

    showCalendar = false;
    showMonthDropdown = false;
    showYearDropdown = false;

    currentDate = new Date();
    selectedDate: Date | null = null;
    displayMonth: number;
    displayYear: number;

    weekdays = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
    months = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
    ];

    years: number[] = [];
    calendarDays: CalendarDay[] = [];

    value: string | null = '';
    control = new FormControl<string | null>('');
    isTemplateRef = isTemplateRef;

    private valueChangesSubscription: Subscription | null = null;

    constructor(@Inject(ElementRef) private elementRef: ElementRef) {
        this.displayMonth = this.currentDate.getMonth();
        this.displayYear = this.currentDate.getFullYear();
    }

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private onChange: (value: string | null) => void = () => { };
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private onTouched: () => void = () => { };

    ngOnInit(): void {
        this.valueChangesSubscription = this.control.valueChanges.subscribe(
            value => {
                this.value = value;
                // Only call onChange with the value if it's not null
                this.onChange(value || ''); // Convert null to empty string if needed
                // OR if you want to pass null: this.onChange(value);
            }
        );

        this.generateYears();
        this.generateCalendarDays();
    }

    ngOnDestroy(): void {
        if (this.valueChangesSubscription) {
            this.valueChangesSubscription.unsubscribe();
        }
    }

    writeValue(value: string | null): void {
        this.value = value;
        if (value && typeof value === 'string') {
            try {
                const parsedDate = parse(value, this.format(), new Date());
                if (isValid(parsedDate)) {
                    this.selectedDate = parsedDate;
                    this.displayMonth = getMonth(parsedDate);
                    this.displayYear = getYear(parsedDate);
                    const formattedDate = this.formatDate(parsedDate); // Format the date before updating the control
                    this.control.setValue(formattedDate, { emitEvent: false });
                }
            } catch (error) {
                console.error('Error parsing date:', error);
                this.selectedDate = null;
            }
        } else if (value === null) {
            this.selectedDate = null;
            this.control.setValue('', { emitEvent: false });
        }
        this.generateCalendarDays();
    }

    registerOnChange(fn: (value: string | null) => void): void {
        this.onChange = fn;
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    registerOnTouched(fn: any): void {
        this.onTouched = fn;
    }

    setDisabledState(isDisabled: boolean): void {
        if (isDisabled) {
            this.control.disable();
        } else {
            this.control.enable();
        }
    }

    onBlur(): void {
        if (!this.showCalendar) {
            this.onTouched();
        }
    }

    hasError(): boolean {
        return (
            !!this.errorMessage() || (this.control.invalid && this.control.touched)
        );
    }

    openCalendar(): void {
        this.showCalendar = true;
    }

    closeCalendar(): void {
        this.showCalendar = false;
        this.onBlur();
    }

    toggleMonthDropdown(event: Event): void {
        event.stopPropagation();
        this.showMonthDropdown = !this.showMonthDropdown;
        this.showYearDropdown = false;
    }

    toggleYearDropdown(event: Event): void {
        event.stopPropagation();
        this.showYearDropdown = !this.showYearDropdown;
        this.showMonthDropdown = false;
    }

    selectMonth(month: number, event: Event): void {
        event.stopPropagation();
        this.displayMonth = month;

        if (this.selectedDate) {
            // Preserve the day of the month
            const day = this.selectedDate.getDate();
            const daysInMonth = new Date(
                this.displayYear,
                this.displayMonth + 1,
                0
            ).getDate();
            // Ensure the day is within the valid range of the new month
            const newDay = Math.min(day, daysInMonth);
            this.selectedDate = new Date(this.displayYear, this.displayMonth, newDay);
            this.control.setValue(this.formatDate(this.selectedDate));
            this.onChange(this.formatDate(this.selectedDate)); // Notify Angular Forms
        }

        this.showMonthDropdown = false;
        this.generateCalendarDays();
    }

    selectYear(year: number, event: Event): void {
        event.stopPropagation();
        this.displayYear = year;

        if (this.selectedDate) {
            // Preserve the day of the month
            const day = this.selectedDate.getDate();
            const month = this.selectedDate.getMonth();
            const daysInMonth = new Date(year, month + 1, 0).getDate();
            // Ensure the day is within the valid range
            const newDay = Math.min(day, daysInMonth);
            this.selectedDate = new Date(year, month, newDay);
            this.control.setValue(this.formatDate(this.selectedDate));
        }

        this.showYearDropdown = false;
        this.generateCalendarDays();
    }

    selectDate(day: CalendarDay): void {
        if (!this.isDisabled(day)) {
            this.selectedDate = day.date;
            const formattedDate = this.formatDate(day.date);
            this.control.setValue(formattedDate);
            this.onChange(formattedDate); // Explicitly pass the string value
            this.dateSelected.emit(day.date);
            this.showCalendar = false;
            this.onBlur();
            this.generateCalendarDays();
        }
    }

    generateYears(): void {
        for (let year = this.minYear(); year <= this.maxYear(); year++) {
            this.years.push(year);
        }
        this.years.reverse(); // Display most recent years first
    }

    generateCalendarDays(): void {
        this.calendarDays = [];

        // Get first day of the month
        const firstDayOfMonth = startOfMonth(
            new Date(this.displayYear, this.displayMonth)
        );
        const lastDayOfMonth = endOfMonth(
            new Date(this.displayYear, this.displayMonth)
        );

        const firstDayWeekday = firstDayOfMonth.getDay();

        // Add days from previous month to fill the first row
        for (let i = 0; i < firstDayWeekday; i++) {
            const date = new Date(
                this.displayYear,
                this.displayMonth - 1,
                new Date(this.displayYear, this.displayMonth, 0).getDate() -
                (firstDayWeekday - i - 1)
            );

            this.calendarDays.push({
                date,
                isCurrentMonth: false,
                isSelected: this.isSelectedDate(date),
                isToday: isToday(date),
            });
        }

        // Add days of current month
        const daysOfMonth = eachDayOfInterval({
            start: firstDayOfMonth,
            end: lastDayOfMonth,
        });

        for (const date of daysOfMonth) {
            this.calendarDays.push({
                date,
                isCurrentMonth: true,
                isSelected: this.isSelectedDate(date),
                isToday: isToday(date),
            });
        }

        const remainingCells =
            CALENDAR_GRID_COLUMNS * CALENDAR_GRID_ROWS - this.calendarDays.length;
        for (let i = 1; i <= remainingCells; i++) {
            const date = new Date(this.displayYear, this.displayMonth + 1, i);
            this.calendarDays.push({
                date,
                isCurrentMonth: false,
                isSelected: this.isSelectedDate(date),
                isToday: isToday(date),
            });
        }
    }

    isSelectedDate(date: Date): boolean {
        if (!this.selectedDate) return false;
        return (
            date.getDate() === this.selectedDate.getDate() &&
            date.getMonth() === this.selectedDate.getMonth() &&
            date.getFullYear() === this.selectedDate.getFullYear()
        );
    }

    formatDate(date: Date): string {
        return format(date, DATE_FORMAT);
    }

    parseInputDate(event: Event): void {
        const input = (event.target as HTMLInputElement).value;
        const parsed = parse(input, DATE_FORMAT, new Date());
        if (isValid(parsed)) {
            this.selectedDate = new Date(parsed);
            this.displayMonth = getMonth(parsed);
            this.displayYear = getYear(parsed);
            this.generateCalendarDays();
        }
    }

    dynamicClasses() {
        const classes: Record<string, boolean> = {
            'bottom-20': this.position() === 'top' && this.hasError(),
            'bottom-14': this.position() === 'top',
            'mt-1': this.position() === 'bottom',
        };

        if (this.rightOffset() !== null) {
            classes[`right-${this.rightOffset()}`] = true;
        }

        return classes;
    }

    // Checks if a day is disabled based on current month the min and max dates
    isDisabled(day: CalendarDay): boolean {
        const minDate = this.minDate();
        const maxDate = this.maxDate();

        if (!day.isCurrentMonth) {
            return true;
        }

        if (minDate && day.date < minDate) return true;
        if (maxDate && day.date > maxDate) return true;

        return false;
    }
}
