<div class="relative" libClickOutside (clickOutside)="showCalendar && closeCalendar()"
    (click)="$event.stopPropagation()" tabindex="0" (keydown)="$event.stopPropagation()"
    (keyup)="$event.stopPropagation()">
    @if (label()) {
    <label class="block text-xs text-primary mb-2" [for]="control">
        {{ label() }}
    </label>
    }

    <!-- Input field -->
    <div class="relative">
        <div class="absolute inset-y-0 left-0 flex items-center pl-3.5 text-neutral-dark cursor-pointer"
            (click)="openCalendar()" (click)="$event.stopPropagation()" tabindex="0"
            (keydown)="$event.stopPropagation()" (keyup)="$event.stopPropagation()">
            <img src="/assets/icons/calendar.svg" alt="calendar" [ngClass]="{
          'max-390:w-4 max-390:h-4': useResponsiveFontSize(),
        }" />
        </div>
        <input type="text" readonly [formControl]="control" [name]="name()" [placeholder]="placeholder() || format()"
            (focus)="openCalendar()" (input)="parseInputDate($event)" (blur)="onBlur()"
            class="text-sm placeholder:text-sm w-full pl-10 px-4 py-3.5 border border-surface rounded-xl focus:outline-none focus:ring-1 focus:ring-secondary-blue placeholder:text-neutral-dark placeholder:overflow-visible"
            [ngClass]="{
        'border-none border-0 ring-1': showCalendar || hasError(),
        'ring-secondary-blue': showCalendar,
        'ring-feedback-error': hasError(),
        'max-390:text-[10px] max-390:placeholder:text-[10px] max-390:pl-[2.2rem]':
          useResponsiveFontSize(),
      }" (click)="$event.stopPropagation()" />
    </div>

    <!-- Error message -->
    @if (hasError()) {
    <div class="text-feedback-error text-xs mt-2">
        @if (isTemplateRef(errorMessage())) {
        <ng-container *ngTemplateOutlet="$any(errorMessage())"></ng-container>
        } @else {
        {{ errorMessage() }}
        }
    </div>
    }

    <!-- Calendar dropdown -->
    @if (showCalendar) {
    <div #calendarContainer
        class="absolute min-w-60 w-fit md:w-max p-5 md:p-6 z-30 bg-surface-white rounded-3xl shadow-lg border border-surface overflow-hidden"
        [ngClass]="dynamicClasses()">
        <!-- Calendar header -->
        <div class="flex mb-6 items-center justify-between">
            <!-- Month/Year selector -->
            <div class="flex items-center space-x-1">
                <!-- Month dropdown -->
                <div class="relative">
                    <div class="rounded-3xl border border-surface focus-within:ring-1 focus-within:ring-secondary-blue">
                        <button type="button"
                            class="px-4 py-3.5 text-sm font-medium flex items-center justify-between gap-2 lg:gap-12 lg:min-w-40"
                            (click)="toggleMonthDropdown($event)">
                            {{ months[displayMonth] }}
                            <img src="assets/icons/chevron-down.svg" alt="Down Arrow" />
                        </button>
                    </div>

                    <!-- Month dropdown menu -->
                    @if (showMonthDropdown) {
                    <div
                        class="absolute w-max md:w-full left-0 mt-1 bg-surface-white rounded-xl shadow-lg border border-surface z-10 max-h-60 overflow-y-auto">
                        <div class="py-2">
                            @for (month of months; track $index) {
                            <button type="button" (click)="selectMonth($index, $event)"
                                class="block w-full text-left px-4 py-2 text-sm hover:bg-surface-lightest"
                                [class.font-medium]="$index === displayMonth"
                                [class.text-blue-500]="$index === displayMonth">
                                {{ month }}
                            </button>
                            }
                        </div>
                    </div>
                    }
                </div>
            </div>

            <!-- Year dropdown -->
            <div class="relative">
                <div
                    class="relative flex items-center gap-2 bg-center rounded-3xl border border-surface focus-within:ring-1 focus-within:ring-secondary-blue">
                    <button type="button" class="text-sm font-medium flex items-center gap-2 lg:gap-6 px-4 py-3.5"
                        (click)="toggleYearDropdown($event)">
                        {{ displayYear }}
                        <img src="assets/icons/chevron-down.svg" alt="Down Arrow" />
                    </button>
                </div>

                <!-- Year dropdown menu -->
                @if (showYearDropdown) {
                <div
                    class="absolute w-full right-0 mt-1 bg-surface-white rounded-xl shadow-lg border border-surface z-10 max-h-60 overflow-y-auto">
                    <div class="py-2">
                        @for (year of years; track $index) {
                        <button type="button" (click)="selectYear(year, $event)"
                            class="block w-full text-left px-4 py-2 text-sm hover:bg-surface-lightest"
                            [class.font-medium]="year === displayYear" [class.text-blue-500]="year === displayYear">
                            {{ year }}
                        </button>
                        }
                    </div>
                </div>
                }
            </div>
        </div>

        <!-- Calendar grid -->
        <div>
            <!-- Weekdays header -->
            <div class="grid grid-cols-7 my-1">
                @for (day of weekdays; track $index) {
                <div class="h-9 w-9 text-center text-sm text-neutral-dark flex items-center justify-center">
                    {{ day }}
                </div>
                }
            </div>

            <!-- Calendar days -->
            <div class="grid grid-cols-7 gap-1">
                @for (day of calendarDays; track $index) {
                <button type="button" (click)="selectDate(day)"
                    class="h-9 w-9 flex items-center justify-center text-sm rounded-[14px]" [ngClass]="{
                'bg-blue-500': day.isSelected,
                'text-surface-white': day.isSelected,
                'font-medium': day.isToday || day.isSelected,
                'text-neutral-medium': isDisabled(day),
                'max-height-720:h-7 max-height-720:w-7': useResponsiveHeight(),
              }" [disabled]="isDisabled(day)">
                    {{ day.date.getDate() }}
                </button>
                }
            </div>
        </div>
    </div>
    }
</div>