
import { CommonModule } from '@angular/common';
import { Component, forwardRef, input, TemplateRef } from '@angular/core';
import { FormControl, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { isTemplateRef } from '../../../utils';

@Component({
    selector: 'lib-text-input',
    imports: [CommonModule, ReactiveFormsModule],
    templateUrl: './text-input.component.html',
    styleUrl: './text-input.component.css',
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => TextInputComponent),
            multi: true,
        },
    ]
})
export class TextInputComponent {
    type = input<'text' | 'email' | 'password' | 'tel'>('text');
    showPasswordToggle = input<boolean>(false);
    name = input<string>('');
    label = input<string>('');
    placeholder = input<string>('');
    required = input<boolean>(false);
    disabled = input<boolean>(false);
    errorMessage = input<string | null | TemplateRef<Component>>(null);
    icon = input<string | null>(null);
    iconAlt = input<string | null>(null);
    customClass = input<string | null>(null);
    description = input<string | null>(null);
    onIconClick = input<(() => void) | null>(null);
    showPassword = false;
    value: string | null = '';
    control = new FormControl('');
    isTemplateRef = isTemplateRef;
    private valueChangesSubscription: Subscription | null = null;

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private onChange: (value: string | null) => void = () => { };
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private onTouched: () => void = () => { };

    ngOnInit(): void {
        this.valueChangesSubscription = this.control.valueChanges.subscribe(
            value => {
                this.value = value;
                this.onChange(value);
            }
        );
    }

    ngOnDestroy(): void {
        if (this.valueChangesSubscription) {
            this.valueChangesSubscription.unsubscribe();
        }
    }

    writeValue(value: string | null): void {
        this.value = value;
        this.control.setValue(value, { emitEvent: false });
    }

    registerOnChange(fn: (value: string | null) => void): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: () => void): void {
        this.onTouched = fn;
    }

    setDisabledState(isDisabled: boolean): void {
        if (isDisabled) {
            this.control.disable();
        } else {
            this.control.enable();
        }
    }

    onBlur(): void {
        this.onTouched();
    }

    togglePasswordVisibility(): void {
        this.showPassword = !this.showPassword;
    }

    getInputType(): string {
        if (this.type() === 'password') {
            return this.showPassword ? 'text' : 'password';
        }
        return this.type();
    }

    hasError(): boolean {
        return (
            !!this.errorMessage() || (this.control.invalid && this.control.touched)
        );
    }

    dynamicClass(): string {
        let className = '';
        if (this.hasError()) {
            className = 'border-none ring-1 ring-feedback-error';
        } else if (this.description()) {
            className = 'border-none ring-1 ring-surface';
        }
        if (this.customClass()) {
            className += ` ${this.customClass()}`;
        }
        return className;
    }
}
