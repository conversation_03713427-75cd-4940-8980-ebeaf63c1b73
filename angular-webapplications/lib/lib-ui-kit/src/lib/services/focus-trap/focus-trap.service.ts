import { Injectable, Renderer2, Inject, PLATFORM_ID } from '@angular/core';
import { DOCUMENT, isPlatformBrowser } from '@angular/common';

@Injectable({
  providedIn: 'root',
})
export class FocusTrapService {
  // Store the element that had focus before modal/sidebar opened
  private previouslyFocusedElement: HTMLElement | null = null;

  // Store first and last focusable elements
  private firstFocusableElement: HTMLElement | null = null;
  private lastFocusableElement: HTMLElement | null = null;

  // Store the tabindex values of elements outside the trapped container
  private elementsWithTabIndex: { element: HTMLElement; tabIndex: string }[] =
    [];

  constructor(
    @Inject(DOCUMENT) private document: Document,
    @Inject(PLATFORM_ID) private platformId: object
  ) {}

  /**
   * Trap focus within a specific container element
   * @param containerElement The element to trap focus within
   * @param renderer The Angular Renderer2 instance
   */
  trapFocus(containerElement: HTMLElement, renderer: Renderer2): void {
    if (isPlatformBrowser(this.platformId)) {
      // Store current active element to restore later
      this.previouslyFocusedElement = this.document
        .activeElement as HTMLElement;
      if (this.previouslyFocusedElement) {
        this.previouslyFocusedElement.blur();
      }

      // Store and disable focus on all elements outside container
      this.disableTabindexOutsideContainer(containerElement, renderer);

      // Get all focusable elements inside container
      const focusableElements = this.getFocusableElements(containerElement);

      if (focusableElements.length > 0) {
        this.firstFocusableElement = focusableElements[0];
        this.lastFocusableElement =
          focusableElements[focusableElements.length - 1];

        // Focus first element
        setTimeout(() => this.firstFocusableElement?.focus(), 0);
      } else {
        // If no focusable elements, focus the container itself
        renderer.setAttribute(containerElement, 'tabindex', '0');
        setTimeout(() => containerElement.focus(), 0);
      }

      // Block background scrolling
      this.blockBackgroundScrolling();
    }
  }

  /**
   * Handle tab key navigation within the focus trap
   * @param event The keyboard event
   */
  handleTabKey(event: KeyboardEvent): void {
    if (isPlatformBrowser(this.platformId)) {
      if (event.key !== 'Tab') return;

      // If there are no focusable elements, just prevent tabbing
      if (!this.firstFocusableElement || !this.lastFocusableElement) {
        event.preventDefault();
        return;
      }

      // If shift + tab pressed and focus is on first element, move to last element
      if (
        event.shiftKey &&
        this.document.activeElement === this.firstFocusableElement
      ) {
        event.preventDefault();
        this.lastFocusableElement.focus();
      }
      // If tab pressed and focus is on last element, move to first element
      else if (
        !event.shiftKey &&
        this.document.activeElement === this.lastFocusableElement
      ) {
        event.preventDefault();
        this.firstFocusableElement.focus();
      }
    }
  }

  /**
   * Restore focus to the previously focused element and cleanup
   * @param renderer The Angular Renderer2 instance
   */
  releaseFocus(renderer: Renderer2): void {
    if (isPlatformBrowser(this.platformId)) {
      // Re-enable tabindex on all elements outside container
      this.restoreTabindexOutsideContainer(renderer);

      // Restore focus to previously focused element
      if (this.previouslyFocusedElement) {
        setTimeout(() => this.previouslyFocusedElement?.focus(), 0);
        this.previouslyFocusedElement = null;
      }

      // Reset focusable elements
      this.firstFocusableElement = null;
      this.lastFocusableElement = null;

      // Allow background scrolling
      this.allowBackgroundScrolling();
    }
  }

  /**
   * Get all focusable elements within a container
   * @param element The container element to search within
   * @returns Array of focusable HTML elements
   */
  private getFocusableElements(element: HTMLElement): HTMLElement[] {
    if (isPlatformBrowser(this.platformId)) {
      const selector =
        'a[href], button:not([disabled]), input:not([disabled]), ' +
        'select:not([disabled]), textarea:not([disabled]), ' +
        '[tabindex]:not([tabindex="-1"]), [contenteditable]';

      return Array.from(element.querySelectorAll(selector)) as HTMLElement[];
    }
    return [];
  }

  /**
   * Disable tabindex on elements outside the focus trap container
   * @param containerElement The container element to trap focus within
   * @param renderer The Angular Renderer2 instance
   */
  private disableTabindexOutsideContainer(
    containerElement: HTMLElement,
    renderer: Renderer2
  ): void {
    if (isPlatformBrowser(this.platformId)) {
      // Clear previous stored elements
      this.elementsWithTabIndex = [];

      // Get all focusable elements in the document
      const allFocusableElements = this.getFocusableElements(
        this.document.body
      );

      // For each focusable element, if it's not in the container, store its tabindex and set it to -1
      allFocusableElements.forEach(element => {
        if (!containerElement.contains(element)) {
          this.elementsWithTabIndex.push({
            element,
            tabIndex: element.getAttribute('tabindex') || '',
          });

          renderer.setAttribute(element, 'tabindex', '-1');
          // Also set aria-hidden for screen readers
          renderer.setAttribute(element, 'aria-hidden', 'true');
        }
      });
    }
  }

  /**
   * Restore tabindex values on elements outside the container
   * @param renderer The Angular Renderer2 instance
   */
  private restoreTabindexOutsideContainer(renderer: Renderer2): void {
    if (isPlatformBrowser(this.platformId)) {
      // Restore original tabindex values
      this.elementsWithTabIndex.forEach(item => {
        if (item.tabIndex) {
          renderer.setAttribute(item.element, 'tabindex', item.tabIndex);
        } else {
          renderer.removeAttribute(item.element, 'tabindex');
        }
        // Remove aria-hidden
        renderer.removeAttribute(item.element, 'aria-hidden');
      });

      // Clear the stored elements
      this.elementsWithTabIndex = [];
    }
  }

  /**
   * Block background scrolling
   */
  blockBackgroundScrolling(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.document.body.classList.add('modal-open');
    }
  }

  /**
   * Allow background scrolling
   */
  allowBackgroundScrolling(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.document.body.classList.remove('modal-open');
    }
  }
}
