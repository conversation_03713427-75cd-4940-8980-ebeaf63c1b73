import { Directive, ElementRef, HostListener, Inject, output, PLATFORM_ID } from '@angular/core';

@Directive({
    selector: '[libClickOutside]',
})
export class ClickOutsideDirective {
    clickOutside = output<void>();

    constructor(private elementRef: ElementRef) { }

    @HostListener('document:click', ['$event.target'])
    @HostListener('document:touchend', ['$event'])
    public onClickOrTouch(target: EventTarget | TouchEvent): void {
        let clickedInside = false;

        if (target instanceof TouchEvent) {
            if (target.changedTouches.length > 0) {
                const touch = target.changedTouches[0];
                const touchTarget = document.elementFromPoint(
                    touch.clientX,
                    touch.clientY
                );
                clickedInside = this.elementRef.nativeElement.contains(touchTarget);
            }
        } else if (target instanceof HTMLElement) {
            clickedInside = this.elementRef.nativeElement.contains(target);
        }

        if (!clickedInside) {
            this.clickOutside.emit();
        }
    }
}
