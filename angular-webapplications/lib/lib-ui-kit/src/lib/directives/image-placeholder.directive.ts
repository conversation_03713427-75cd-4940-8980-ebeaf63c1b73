import { Directive, ElementRef, HostListener, inject, input } from '@angular/core';
import { appCoreConstant } from 'lib-app-core';

@Directive({
    selector: '[libImagePlaceholder]',
    standalone: true
})
export class ImagePlaceholderDirective {
    placeholderSrc = input<string>(appCoreConstant.IMAGES.PLACEHOLDER);
    private readonly elementRef = inject(ElementRef<HTMLImageElement>);

    @HostListener('error')
    onError(): void {
        const img = this.elementRef.nativeElement;
        if (img.src !== this.placeholderSrc()) {
            img.src = this.placeholderSrc();
        }
    }
} 