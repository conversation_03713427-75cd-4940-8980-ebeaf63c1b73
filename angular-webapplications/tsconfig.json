﻿    {
        "compileOnSave": false,
        "compilerOptions": {
            "baseUrl": "./",
            "outDir": "./dist/out-tsc",
            "strict": true,
            "noImplicitOverride": true,
            "noPropertyAccessFromIndexSignature": true,
            "paths": {
                "lib-app-core": [
                    "./lib/lib-app-core/src/public-api.ts"
                ],
                "lib-auth": [
                    "./lib/lib-auth/src/public-api.ts"
                ],
                "@theme/*": [
                    "../angular-themes/default/mustang/*"
                ],
                "lib-my-account": [
                    "./lib/lib-my-account/src/public-api.ts"
                ],
                "lib-ui-kit": [
                    "./lib/lib-ui-kit/src/public-api.ts"
                ]
            },
            "noImplicitReturns": true,
            "noFallthroughCasesInSwitch": true,
            "skipLibCheck": true,
            "isolatedModules": true,
            "esModuleInterop": true,
            "experimentalDecorators": true,
            "moduleResolution": "bundler",
            "importHelpers": true,
            "target": "ES2022",
            "module": "ES2022"
        },
        "angularCompilerOptions": {
            "enableI18nLegacyMessageIdFormat": false,
            "strictInjectionParameters": true,
            "strictInputAccessModifiers": true,
            "strictTemplates": true
        },
        "include": [
            "tailwind.config.ts",
            "projects/**/*",
            "src/**/*",
            "./lib/**/*"
        ]
    }