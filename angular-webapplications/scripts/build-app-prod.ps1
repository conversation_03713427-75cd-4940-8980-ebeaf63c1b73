<#
.AUTHOR
    Rameez Raza

.DESCRIPTION
    This script automates Angular environment setup, builds the application, and prepares the deployment.

.NOTES
    Ensure that Node.js and npm are installed before running this script.
    .\build-app-prod.ps1 -releaseDir "C:\releases" -releaseTag "mustang" -themeName "bana-hills" -applicationName "online-waiver"
#>

param (
    [string]$releaseDir,
    [string]$releaseTag,
    [string]$themeName = "default",
    [string]$applicationName
)

# Validate required parameters
if (-not $releaseDir -or -not $releaseTag -or -not $applicationName) {
    Write-Host "Error: Missing required parameters." -ForegroundColor Red
    Write-Host "Usage: .\build-app.ps1 -releaseDir <path> -releaseTag <tag> -themeName <theme> -applicationName <app>" -ForegroundColor Yellow
    exit
}

# Read configuration from config.json
# $configFile = ".\config.json"

# if (!(Test-Path -Path $configFile)) {
#     Write-Host "Error: Config file $configFile not found. Exiting..." -ForegroundColor Red
#     exit
# }

# $config = Get-Content $configFile | ConvertFrom-Json

# # Override releaseDir if not provided in arguments
# if (-not $releaseDir) {
#     $releaseDir = $config.outputDir
# }

# if (-not $releaseDir) {
#     Write-Host "Error: releaseDir is missing in config.json. Exiting..." -ForegroundColor Red
#     exit
# }

Write-Host "Using Release Directory: $releaseDir"
Write-Host "Release Tag: $releaseTag"
Write-Host "Theme Name: $themeName"
Write-Host "Application Name: $applicationName"

# Define paths
$applicationRepo = "angular-webapplications"
$themeRepo = "angular-themes"
$applicationPath = "$releaseDir\$releaseTag\$applicationRepo"
$themePath = "$releaseDir\$themeRepo"

# $themeRepoPath = "$releaseDir\$releaseTag\$themeRepo"

$themeReleasePath = "$themePath\$themeName\$releaseTag"
$themeRelease = "$releaseTag"

$nodeEnvOutFile = "$applicationPath\dist\config\.env"
$webConfigSourcePath = "$applicationPath\projects\config\iis\web.config"
$webConfigDestPath = "$applicationPath\dist\$applicationName\web.config"
$tsconfigPath = "$applicationPath\tsconfig.json"


# Download repo
# .\download-repo.ps1 -repoName "angular-webapplications" -releaseTag $releaseTag
# .\download-repo.ps1 -repoName "angular-themes" -releaseTag $themeName
.\download-repo.ps1 -repoName $applicationRepo -releaseTag $releaseTag -extractPath $applicationPath
.\download-repo.ps1 -repoName $themeRepo -releaseTag $themeName -extractPath $themePath

Write-Host "Setting up Application." -ForegroundColor Green
# exit 1


# Validate required directories
if (!(Test-Path -Path $applicationPath)) {
    Write-Host "Error: Application directory $applicationPath does not exist. Exiting..." -ForegroundColor Red
    exit
}

# Validate required directories
if (!(Test-Path -Path $themePath)) {
    Write-Host "Error: Theme directory $themePath does not exist. Exiting..." -ForegroundColor Red
    exit
}

# -------------------------------------
$releasesFile = ".\releases.json"

# Validate JSON file existence
if (!(Test-Path -Path $releasesFile)) {
    Write-Host "Error: JSON file not found at $releasesFile" -ForegroundColor Red
    exit 1
}

$releasesFile = ".\releases.json"

if (Test-Path -Path $themeReleasePath) {
    Write-Host "Found release directory: $themeReleasePath" -ForegroundColor Green
} else {
    # Write-Host "Release directory not found: $themeReleasePath. Checking older releases..." -ForegroundColor Yellow
    
    # Read JSON file
    if (!(Test-Path -Path $releasesFile)) {
        Write-Host "Error: Release JSON file not found at $releasesFile" -ForegroundColor Red
        exit 1
    }

    $releases = Get-Content $releasesFile | ConvertFrom-Json

    # Sort releases by ID in descending order
    $sortedReleases = $releases | Sort-Object -Property id -Descending

    # Find an existing older release
    foreach ($release in $sortedReleases) {
        $themeRelease = "$($release.name)"
        $themeReleasePath = "$themePath\$themeName\$themeRelease"
        Write-Host "Checking old $themeReleasePath" -ForegroundColor Red
        if (Test-Path -Path $themeReleasePath) {
            Write-Host "Found older release directory: $themeReleasePath" -ForegroundColor Green
            break
        }
        $themeReleasePath = $null
    }

    # If no valid release is found, throw error
    if (-not $themeReleasePath) {
        Write-Host "Error: No valid release directory found in $themeReleasePath" -ForegroundColor Red
        exit 1
    }
}

Write-Host "Using theme release path: $themeReleasePath" -ForegroundColor Cyan

# ----------------------------


# if (!(Test-Path -Path $themePath)) {
#     Write-Host "Error: Theme directory $themePath does not exist. Exiting..." -ForegroundColor Red
#     exit
# }

# Set up theme symbolic link
$linkPath = "$applicationPath\theme"
if (Test-Path $linkPath) { Remove-Item $linkPath -Force -Recurse }
Write-Host "Setting up theme: $themeName"
New-Item -ItemType SymbolicLink -Path $linkPath -Target $themeReleasePath

# Update tsconfig for the theme
.\update-tsconfig.ps1 "../../angular-themes/$themeName/$themeRelease/*" $tsconfigPath
# exit 1

# Install dependencies and build application
Write-Host "Installing Angular Dependencies..."
Set-Location $applicationPath
npm install

Write-Host "Building Application: $applicationName..."
npm run build $applicationName --configuration=production

if ($LASTEXITCODE -ne 0) {
    Write-Host "Angular build failed with exit code $LASTEXITCODE" -ForegroundColor Red
    exit $LASTEXITCODE
} else {
    Write-Host "Angular build succeeded" -ForegroundColor Green
}

Write-Host "Application built successfully"

# Create Node.js .env file
$nodeEnvDir = Split-Path -Path $nodeEnvOutFile -Parent
if (!(Test-Path -Path $nodeEnvDir)) {
    Write-Host "Creating config directory..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $nodeEnvDir -Force | Out-Null
}
$nodeEnvContent = @"
PORT=4200
PARAFAIT_API_BASEURL=
APP_NODEAPI_BASEURL=
LOGIN_ID=
PASSWORD=
"@
$nodeEnvContent | Out-File -Encoding utf8 -FilePath $nodeEnvOutFile
Write-Host "Node.js environment file created: $nodeEnvOutFile"

# Copy web.config file
Write-Host "Copying web.config file..."
Copy-Item -Path $webConfigSourcePath -Destination $webConfigDestPath -Force
Write-Host "File copied successfully to: $webConfigDestPath" -ForegroundColor Green

# Define target binaries path
$binariesPath = "$releaseDir\$releaseTag\binaries\$themeName\dist"

# Ensure the target directory exists
if (!(Test-Path -Path $binariesPath)) {
    Write-Host "Creating binaries directory: $binariesPath" -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $binariesPath -Force | Out-Null
}

# Copy the built application to binaries directory
Write-Host "Copying built application to: $binariesPath" -ForegroundColor Green
Copy-Item -Path "$applicationPath\dist\*" -Destination $binariesPath -Recurse -Force

Write-Host "Application successfully copied to $binariesPath" -ForegroundColor Green


# Set-Location "C:\site-workspace\angular-webapplications\scripts"