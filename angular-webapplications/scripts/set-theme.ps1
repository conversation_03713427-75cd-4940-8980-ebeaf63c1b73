<#
.AUTHOR
    Ram<PERSON>
#>

param (
    [Parameter(Mandatory = $true)]
    [string]$releaseTag,

    [string]$themeName = "default"
)

# Show error and exit if required parameter is missing (handled by Mandatory flag)
Write-Host "Preparing symbolic links for theme '$themeName' and release tag '$releaseTag'..."

# Define symbolic link paths and corresponding targets
$links = @(
    @{ Path = '..\theme';    Target = "..\..\angular-themes\$themeName\$releaseTag" },
    @{ Path = '..\partials'; Target = "..\..\datafiles\partials" },
    @{ Path = '..\data';     Target = "..\..\datafiles\data" },
    @{ Path = '..\i18n';     Target = "..\..\datafiles\i18n" }
)

# Remove existing symbolic links (if any)
foreach ($link in $links) {
    if (Test-Path $link.Path) {
        Remove-Item $link.Path -Force -Recurse
    }
}
Write-Host "Removed existing Symbolic links" -ForegroundColor Green

# Create symbolic links
foreach ($link in $links) {
    New-Item -ItemType SymbolicLink -Path $link.Path -Target $link.Target | Out-Null
}
Write-Host "Symbolic links created." -ForegroundColor Green

# Update tsconfig
.\update-tsconfig.ps1 -themePath "../angular-themes/$themeName/$releaseTag/*" -tsconfigPath "../tsconfig.json"

Write-Host "tsconfig updated successfully." -ForegroundColor Green
