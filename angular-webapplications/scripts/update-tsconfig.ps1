<#
.AUTHOR
    Rameez Raza
    .\scripts\update-tsconfig.ps1 -themePath "../angular-themes/default/mustang/*" -tsconfigPath "C:\site-workspace\angular-webapplications\tsconfig.json"
#>

# Get the theme path from the argument (default: "../angular-themes/pnl/*")
param(
    [string]$themePath,
    [string]$tsconfigPath
)


# Validate required parameters
if (-not $themePath -or -not $tsconfigPath) {
    Write-Host "Error: Missing required parameters." -ForegroundColor Red
    Write-Host "Usage: .\update-tsconfig.ps1 themePath <tag> tsconfigPath <theme>" -ForegroundColor Yellow
    exit
}


# Define the path to tsconfig.json
# $tsconfigPath = "tsconfig.json"
Write-Host "tscongig path: $tsconfigPath"

# Check if the file exists before proceeding
if (!(Test-Path $tsconfigPath)) {
    Write-Host "Error: tsconfig.json not found!" -ForegroundColor Red
    exit 1
}

# Read and validate tsconfig.json
try {
    $jsonString = Get-Content $tsconfigPath -Raw -Encoding UTF8
    # $jsonString = $jsonString -replace '//.*', ''  # Remove single-line comments
    # $jsonString = $jsonString -replace '/\*[\s\S]*?\*/', ''  # Remove multi-line comments
    $tsconfigContent = $jsonString | ConvertFrom-Json
} catch {
    Write-Host "Error: Failed to parse tsconfig.json. Ensure it's valid JSON." -ForegroundColor Red
    exit 1
}

# Ensure required properties exist
if (-not $tsconfigContent.PSObject.Properties["compilerOptions"]) {
    $tsconfigContent | Add-Member -MemberType NoteProperty -Name "compilerOptions" -Value @{}
}
if (-not $tsconfigContent.compilerOptions.PSObject.Properties["paths"]) {
    $tsconfigContent.compilerOptions | Add-Member -MemberType NoteProperty -Name "paths" -Value @{}
}

# Update the theme path
$tsconfigContent.compilerOptions.paths."@theme/*" = @($themePath)

# Convert back to JSON with 4-space indentation
$updatedJson = $tsconfigContent | ConvertTo-Json -Depth 10

# Fix formatting to enforce 4-space indentation
$formattedJson = $updatedJson | Out-String | ForEach-Object { $_ -replace '^( *)', '    ' }

# Save the formatted JSON
$formattedJson | Out-File -FilePath $tsconfigPath -Encoding UTF8

Write-Host "Updated tsconfig.json with theme path: $themePath (Formatted with 4-space indentation)" -ForegroundColor Green