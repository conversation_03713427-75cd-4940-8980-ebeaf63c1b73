<#
.AUTHOR
    Rameez Raza

.DESCRIPTION
    This script allows to setup the application for dev and theming

.NOTES
    Ensure that Node.js and npm are installed before running this script.

    .\build-app-prod.ps1 -applicationName "online-waiver" -releaseTag "mustang" -themeName "default/bana-hills/pnl"

#>

param (
    [string]$applicationName,
    [string]$releaseTag,
    [string]$themeName = "default"
)

# Validate required parameters
if (-not $releaseTag -or -not $themeName -or -not $applicationName) {
    Write-Host "Error: Missing required parameters." -ForegroundColor Red
    Write-Host "Usage: .\build-app.ps1 -applicationName <app> -releaseTag <tag> -themeName <theme>" -ForegroundColor Yellow
    exit
}

# # Define theme paths
# $linkPath = '.\theme'

# # Remove existing symbolic link if it exists
# if (Test-Path $linkPath) {
#     Remove-Item $linkPath -Force -Recurse
# }
# # Create symbolic link to client theme
# New-Item -ItemType SymbolicLink -Path $linkPath -Target "..\angular-themes\$themeName\$releaseTag"

# # update tsconfig 
# .\scripts\update-tsconfig.ps1 "../angular-themes/$themeName/$releaseTag/*"

.\switch-theme.ps1 -releaseTag $releaseTag -themeName $themeName



# Check if the source file exists
if (!(Test-Path $nodeEnvSourcePath)) {
    Write-Host "Error: Source file not found - $nodeEnvSourcePath" -ForegroundColor Red
    exit 1
}

# Install dependencies and build application
Write-Host "Installing Angular Dependencies..."
npm install

# Install dependencies and build application
Write-Host "Installing Nodemon"
npm install -g nodemon

Write-Host "Building Application: $application..."
# Run npm watch command
npm run watch $application

# .\scripts\start-app.ps1 $application