<#
.SYNOPSIS
    Downloads and extracts a GitHub release from a private repository.

.PARAMETER repoName
    Name of the repository.

.PARAMETER releaseTag
    Release tag to download.

.PARAMETER extractPath
    Destination path to extract the release to.

.NOTES
    Requires a config.json file with "githubOwner" and "githubToken".

.EXAMPLE
    .\download-repo.ps1 -repoName "angular-webapplications" -releaseTag "hiller" -extractPath "C:\releases\hiller\angular-webapplications"
    .\download-repo.ps1 -repoName "angular-themes" -releaseTag "pnl" -extractPath "C:\releases\angular-themes"
#>

param (
    [Parameter(Mandatory = $true)][string]$repoName,
    [Parameter(Mandatory = $true)][string]$releaseTag,
    [Parameter(Mandatory = $true)][string]$extractPath
)

# Load GitHub credentials from config.json
$configPath = ".\config.json"
if (-not (Test-Path $configPath)) {
    Write-Host "Error: config.json not found." -ForegroundColor Red
    exit 1
}

$config = Get-Content $configPath | ConvertFrom-Json
$githubOwner = $config.githubOwner
$githubToken = $config.githubToken

if (-not $githubOwner -or -not $githubToken) {
    Write-Host "Error: githubOwner or githubToken missing in config.json." -ForegroundColor Red
    exit 1
}

# Prepare GitHub API URL
$apiUrl = "https://api.github.com/repos/$githubOwner/$repoName/releases/tags/$releaseTag"

# Set headers
$headers = @{
    Authorization = "token $githubToken"
    Accept        = "application/vnd.github.v3+json"
    "User-Agent"  = "$env:USERNAME"
}

Write-Host "Fetching release info for tag '$releaseTag' from $repoName..."

try {
    $releaseInfo = Invoke-RestMethod -Uri $apiUrl -Headers $headers
} catch {
    Write-Host "Error: Failed to fetch release info. $_" -ForegroundColor Red
    exit 1
}

$zipUrl = $releaseInfo.zipball_url

# Prepare temp download paths
$tempZip = [System.IO.Path]::GetTempFileName() + ".zip"
$tempExtract = $tempExtract = "C:\temp-extract"
if (-not (Test-Path $tempExtract)) {
    New-Item -ItemType Directory -Path $tempExtract | Out-Null
}


# Add .NET type for extraction
Add-Type -AssemblyName System.IO.Compression.FileSystem

Write-Host "Downloading release zip..."
Invoke-WebRequest -Uri $zipUrl -Headers $headers -OutFile $tempZip

Write-Host "Extracting zip to temp folder using .NET..."
[System.IO.Compression.ZipFile]::ExtractToDirectory($tempZip, $tempExtract)

# Clean target extract path if it exists
if (Test-Path $extractPath) {
    Write-Host "Cleaning existing extract path: $extractPath"
    Remove-Item $extractPath -Recurse -Force
}

# Move contents from extracted folder (assumes one folder inside zip)
$innerFolder = Get-ChildItem -Path $tempExtract -Directory | Select-Object -First 1

if ($null -eq $innerFolder) {
    Write-Host "Error: No folder found inside extracted zip archive." -ForegroundColor Red
    exit 1
}

# Ensure the destination folder exists
New-Item -ItemType Directory -Force -Path $extractPath | Out-Null

# Move all contents inside the extracted folder to the extractPath
Get-ChildItem -Path $innerFolder.FullName | ForEach-Object {
    Move-Item -Path $_.FullName -Destination $extractPath -Force
}

Write-Host "✅ Release successfully extracted to: $extractPath" -ForegroundColor Green

# Cleanup
if (Test-Path $tempZip) {
    Remove-Item $tempZip -Force
}
if (Test-Path $tempExtract) {
    Remove-Item $tempExtract -Recurse -Force
}
