<#
.AUTHOR
    Rameez Raza

.DESCRIPTION
    This script allows to start the application for dev and theming

.NOTES
    This script is only For dev
    before running this script
    Ensure that Node.js and npm are installed and Application is setup correctly
    If application is not setup, run the below script which will setup the application and run this script internally
    .\scripts\setup-app-dev.ps1
#>

# Check if an argument is passed
if ($args.Count -gt 0) {
    $application = $args[0]
} else {
    Write-Host "Error: No application name provided. Usage: script.ps1 <application_name>" -ForegroundColor Red
    exit 1  # Exit with an error code
}

# Verify that application name is correctly set
Write-Host "Using application: $application" -ForegroundColor Green

# Check if npm is available
if (-not (Get-Command npm -ErrorAction SilentlyContinue)) {
    Write-Host "Error: npm is not installed or not found in PATH." -ForegroundColor Red
    exit 1
}

# Check if nodemon is installed
$nodemonCheck = npm list -g --depth=0 | Select-String "nodemon"

# Install nodemon if not found
if (-not $nodemonCheck) {
    Write-Host "Nodemon is not installed. Installing now..."
    npm install -g nodemon
} else {
    Write-Host "Nodemon is already installed."
}

# Kill any running Node.js processes (to free the port)
Write-Host "Stopping existing Node.js processes..."
taskkill /IM node.exe /F 2>$null  # Redirect errors to avoid noise

# Start Windows Terminal with split panes
Start-Process wt -ArgumentList (
    "new-tab cmd /c `"cd /d C:\site-workspace\angular-webapplications && npm run watch $application`" " +
    "; split-pane -V cmd /c `"cd /d C:\site-workspace\angular-webapplications && npm run serve:ssr:$application`""
)

# Confirm script execution
Write-Host "Script execution completed." -ForegroundColor Green
