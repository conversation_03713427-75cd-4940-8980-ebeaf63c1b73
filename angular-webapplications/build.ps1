# Get current date and time
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"

# Log file paths with timestamp
$successLogFile = "./logs/build_success_$timestamp.log"
$errorLogFile = "./logs/build_errors_$timestamp.log"

# Start the build process
Write-Host "Starting the build process for online-waiver..."
ng build --project=online-waiver > $successLogFile 2> $errorLogFile

# Check if the build was successful
if ($LASTEXITCODE -eq 0) {
    Write-Host "Build completed successfully!"
    Write-Host "Success log is available at $successLogFile."
} else {
    Write-Host "Build failed. Errors have been logged to $errorLogFile."
}
