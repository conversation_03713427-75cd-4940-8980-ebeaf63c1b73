.tab-button {
    display: flex;
    flex: 1;
    flex-direction: column;
    padding: 1rem 1.5rem;
    text-align: center;
    position: relative;
    transition: all;
    transition-duration: 150ms;
}

.active-tab-button {
    background-color: var(--color-surface-white);
    color: var(--color-secondary-blue);
    margin-bottom: -0.25rem;
}

.inactive-tab-button {
    color: var(--color-primary);
}

.rounded-register {
    border-top-left-radius: 2rem;
    border-top-right-radius: 1.5rem;
}

.rounded-login {
    border-top-right-radius: 2rem;
    border-top-left-radius: 1.5rem;
}

.curve-after::after {
    content: '';
    height: 100%;
    width: 2rem;
    background: transparent;
    position: absolute;
    bottom: 4px;
    right: -2rem;
    border-bottom-left-radius: 1.5rem;
    box-shadow: 0 24px 0 0 var(--color-surface-white);
    transition: all;
    transition-duration: 150ms;
}

.curve-before::before {
    content: '';
    height: 100%;
    width: 2rem;
    background: transparent;
    position: absolute;
    bottom: 4px;
    left: -2rem;
    border-bottom-right-radius: 25px;
    box-shadow: 0 20px 0 0 var(--color-surface-white);
    transition: all;
    transition-duration: 150ms;
}