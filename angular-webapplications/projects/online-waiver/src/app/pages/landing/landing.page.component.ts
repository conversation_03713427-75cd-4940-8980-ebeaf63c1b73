/**
 * @fileoverview This is the main landing component (home page) for the application.
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-09-18
 */

import { Component, inject, signal } from '@angular/core';
import { SiteBaseComponent } from '../../core/sitebase.component';
import { ParafaitDefaultContainerService, PARENT_FUNCTION_TOKEN, SiteViewsDTOModel, SiteViewsServiceDL } from 'lib-app-core';
import { PageInterface } from 'lib-app-core';
import { LandingPageTheme } from '@theme/waiver/waiver.theme';
import { CommonModule } from '@angular/common';
import { AppInitService } from '../../core/app-init.service';
import { Router } from '@angular/router';
import { PreviewWaiverService } from '../../core/preview-waiver.service';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { WaiverSetContainerDTO<PERSON>ist, WaiverSetContainerResponse, WaiverSetContainerResponseFromAPI } from '../../models/waiver_set_container.model';
import { PreviewWaiverComponent } from '../../components/preview-waiver/preview-waiver.component';
import { HowToSignWaiverComponent } from '../../components/how-to-sign-waiver/how-to-sign-waiver.component';
import { RegistrationFormWaiverComponent } from '../../components/registration-form-waiver/registration-form-waiver.component';
import { LoginFormComponent } from 'lib-auth';
import { ModalComponent } from 'lib-ui-kit';
import { LanguageContainerServiceDL } from 'lib-app-core';
import { ParafaitDefaultContainerDTOModel } from 'lib/lib-app-core/src/lib/models/parafait-default-container-dto.model';

@Component({
    standalone: true,
    selector: 'app-landing',
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        LoginFormComponent,
        RegistrationFormWaiverComponent,
        ModalComponent,
        HowToSignWaiverComponent,
        PreviewWaiverComponent
    ],
    templateUrl: './landing.page.component.html',
    styleUrl: './landing.page.component.scss',
    providers: [
        {
            provide: PARENT_FUNCTION_TOKEN,
            useFactory: (parent: LandingPageComponent) => {
                return parent.getPageTheme ? () => parent.getPageTheme() : () => { };
            },
            deps: [LandingPageComponent] // Ensure that the parent component is resolved
        }
    ]
})
export class LandingPageComponent extends SiteBaseComponent implements PageInterface {

    appInitService = inject(AppInitService);
    private _languageContainerServiceDL = inject(LanguageContainerServiceDL);
    private _parafaitDefaultContainerService = inject(ParafaitDefaultContainerService);
    private _siteViewsService = inject(SiteViewsServiceDL);

    /* preview waiver */
    waiverSetContainerResponse: WaiverSetContainerResponse | any = null;
    selectedWaiverSet: WaiverSetContainerDTOList | any = null;
    isModalOpen = false; //to close and open modal, this will be replaced by new html

    localVar: any;

    constructor(private router: Router, private previewWaiverService: PreviewWaiverService) {
        super();

        // this._languageContainerServiceDL.subscribeToData(data => {
        //     this.localVar = data;
        // });

        // this._parafaitDefaultContainerService.subscribeToData((dtoList: ParafaitDefaultContainerDTOModel[]) => {
        //     // this.localVar = data;
        //     console.log(dtoList);
        //     this.localVar =
        //         ParafaitDefaultContainerDTOModel.findByName(
        //             dtoList,
        //             'GOOGLE_RECAPTCHA_CLIENT_ID'
        //         );
        // });

        this._siteViewsService.subscribeToData((dtoList: SiteViewsDTOModel[]) => {
            // this.localVar = data;
            console.log(dtoList);
            this.localVar =
                SiteViewsDTOModel.findBySiteId(
                    dtoList,
                    1010
                );
        });
    }

    ngOnInit() {
    }

    protected getRelevantTheme() {
        return LandingPageTheme;
    }

    getPageTheme() {
        return this.getRelevantTheme();
    }

    update(siteId: number) {
        this.appInitService.updateSiteId(siteId).then(() => {
            // this.fetchWaiverSetContainer();
        })
    }

    /* Preview related code */
    //fetch waiver set container response from API 
    selectedWaiver: any = null;
    fetchWaiverSetContainer() {
        this.previewWaiverService.getWaiverSetContainer().subscribe({
            next: (waiverSetContainerResp: WaiverSetContainerResponseFromAPI | any) => {
                if (waiverSetContainerResp && waiverSetContainerResp?.data) {
                    this.waiverSetContainerResponse = waiverSetContainerResp?.data;
                }
            },
            error: (error) => {
                console.log("fetchAndProcessWaiverSetContainer()" + JSON.stringify(error));
            }
        })
    }

    onSelectParticipantAndSignWaiver() {
        let selectedWaiverSetId = 1;  //this as to be replaced with selectedWaiverSetId
        let loggedInCustId = 34;
        this.router.navigate(['/sign-waiver', selectedWaiverSetId, loggedInCustId]);
    }

    openPrevieWaiverComponent() {
        this.isModalOpen = true;
    }


    // Method to open the modal
    openModal() {
        this.isModalOpen = true;
    }

    // Method to close the modal
    closeModal() {
        this.isModalOpen = false;
    }


    onSelectedWaiverSet(event: Event) {
        const selectedOption: any = (event.target as HTMLSelectElement).value;
        this.selectedWaiverSet = this.waiverSetContainerResponse.WaiverSetContainerDTOList.find((ws: WaiverSetContainerDTOList) => ws.WaiverSetId == selectedOption)
    }

    // new html ui code starts 
    readonly showHowToSignWaivers = signal<boolean>(false);
    readonly showPreviewWaiver = signal<boolean>(false);
    activeTab: 'register' | 'login' = 'register';

    setActiveTab(tab: 'register' | 'login'): void {
        this.activeTab = tab;
    }

    closeHowToSignWaivers() {
        this.showHowToSignWaivers.set(false);
    }

    closePreviewWaiver() {
        this.showPreviewWaiver.set(false);
    }
    // new html ui code ends 
}
