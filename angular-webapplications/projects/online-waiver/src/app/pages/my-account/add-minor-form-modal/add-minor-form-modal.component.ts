import { Component, input, output, TemplateRef } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { DATE_FORMAT, DatePickerComponent, TextInputComponent } from 'lib-ui-kit';
import { ErrorMessage } from 'lib-auth';

interface MinorFormData {
    firstName: FormControl<string | null>;
    lastName: FormControl<string | null>;
    dateOfBirth: FormControl<string | null>;
}

const errorMessages: ErrorMessage<MinorFormData> = {
    firstName: {
        required: 'First name is required',
    },
    lastName: {
        required: 'Last name is required',
    },
    dateOfBirth: {
        required: 'Date of birth is required',
    },
};

@Component({
    selector: 'app-add-minor-form-modal',
    imports: [TextInputComponent, DatePickerComponent, ReactiveFormsModule],
    templateUrl: './add-minor-form-modal.component.html',
    styleUrl: './add-minor-form-modal.component.scss'
})
export class AddMinorFormModalComponent {
    minorForm: FormGroup;
    protected readonly dateFormat = DATE_FORMAT;

    constructor(private fb: FormBuilder) {
        this.minorForm = this.fb.group({
            firstName: ['', Validators.required],
            lastName: ['', Validators.required],
            dateOfBirth: ['', Validators.required],
        });
    }

    hasError(
        fieldName: keyof MinorFormData
    ): string | null | TemplateRef<Component> {
        const control = this.minorForm.get(fieldName);

        if (control?.invalid && control?.touched && control.errors) {
            const errorKeys = Object.keys(control.errors);
            if (errorKeys.length > 0) {
                const firstErrorKey = errorKeys[0];
                return errorMessages[fieldName]?.[firstErrorKey] ?? null;
            }
        }

        return null;
    }

    onSubmit() {
        if (this.minorForm.valid) {
            console.log('Form submitted', this.minorForm.value);
        }
    }

}
