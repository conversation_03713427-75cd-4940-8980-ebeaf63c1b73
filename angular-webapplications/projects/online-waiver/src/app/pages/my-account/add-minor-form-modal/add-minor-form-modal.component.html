<div class="p-5 md:p-6 w-[90vw] max-w-[30rem] pt-0 md:pt-1">
    <form [formGroup]="minorForm" class="grid gap-5">
        <!-- First Name -->
        <lib-text-input formControlName="firstName" name="firstName" label="First Name" placeholder="Enter first name"
            [required]="true" [errorMessage]="hasError('firstName')"></lib-text-input>

        <!-- Last Name -->
        <lib-text-input formControlName="lastName" name="lastName" label="Last Name" placeholder="Enter last name"
            [required]="true" [errorMessage]="hasError('lastName')"></lib-text-input>

        <!-- Date of Birth -->
        <lib-date-picker formControlName="dateOfBirth" name="dateOfBirth" label="Date of Birth"
            [placeholder]="dateFormat.toUpperCase()" [format]="dateFormat" [required]="true" position="top"
            [rightOffset]="0" [useResponsiveHeight]="true" [errorMessage]="hasError('dateOfBirth')"></lib-date-picker>

        <button type="submit" [disabled]="minorForm.invalid" (click)="onSubmit()"
            class="py-3 px-4 bg-primary text-white font-medium rounded-4xl disabled:bg-surface">
            Save
        </button>
    </form>
</div>