import { CommonModule } from '@angular/common';
import { Component, input } from '@angular/core';

interface SignedWaiver {
    code: string;
    signedBy: string;
    signedFor: string;
    signedDate: string;
    expiryDate: string;
}

@Component({
    selector: 'app-my-signed-waivers',
    imports: [CommonModule],
    templateUrl: './my-signed-waivers.component.html',
    styleUrl: './my-signed-waivers.component.scss'
})
export class MySignedWaiversComponent {
    signedWaivers = input<SignedWaiver[]>([
        {
            code: 'SJ5F10',
            signedBy: '<PERSON><PERSON>z Raza',
            signedFor: 'Child Raza',
            signedDate: '18-10-2024',
            expiryDate: '18-10-2030',
        },
        {
            code: 'SJ5F20',
            signedBy: '<PERSON><PERSON><PERSON> Raza',
            signedFor: 'Child Raza',
            signedDate: '18-10-2024',
            expiryDate: '18-10-2030',
        },
        {
            code: 'SJ5F30',
            signedBy: '<PERSON><PERSON><PERSON>',
            signedFor: '<PERSON> Ra<PERSON>',
            signedDate: '18-10-2024',
            expiryDate: '18-10-2030',
        },
    ]);

    viewWaiver(code: string): void {
        console.log(`Viewing waiver: ${code}`);
        // Implement view functionality
    }

    downloadWaiver(code: string): void {
        console.log(`Downloading waiver: ${code}`);
        // Implement download functionality
    }
}
