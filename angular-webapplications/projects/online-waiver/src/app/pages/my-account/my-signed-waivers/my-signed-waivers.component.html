<div
    class="flex flex-col gap-5 lg:gap-6 bg-surface-lighter md:bg-surface-white rounded-4xl md:shadow-lg md:p-6 md:min-h-[calc(100vh-168px)]">
    <h1 class="text-lg font-semibold text-primary">My Signed Waivers</h1>

    <!-- Waivers Grid - Responsive layout -->
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <!-- Waiver Card -->
        @for (waiver of signedWaivers(); track waiver.code) {
        <div class="bg-surface-white p-6 rounded-4xl shadow-lg overflow-hidden">
            <!-- Waiver Code Header -->
            <div class="bg-[#0bab68] text-white p-4 text-center rounded-3xl">
                <div class="text-xs mb-0.5 uppercase">Waiver Code</div>
                <div class="text-xl font-bold">{{ waiver.code }}</div>
            </div>

            <!-- Waiver Details -->
            <div class="mt-6">
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <div class="text-xs text-neutral-dark mb-0.5">Signed By</div>
                        <div class="text-sm font-medium">{{ waiver.signedBy }}</div>
                    </div>
                    <div>
                        <div class="text-xs text-neutral-dark mb-0.5">Signed For</div>
                        <div class="text-sm font-medium">{{ waiver.signedFor }}</div>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4 mb-6">
                    <div>
                        <div class="text-xs text-neutral-dark mb-0.5">Signed Date</div>
                        <div class="text-sm font-medium">{{ waiver.signedDate }}</div>
                    </div>
                    <div>
                        <div class="text-xs text-neutral-dark mb-0.5">Expiry Date</div>
                        <div class="text-sm font-medium">{{ waiver.expiryDate }}</div>
                    </div>
                </div>

                <div class="w-full border-b-2 border-surface my-6 opacity-30"></div>
                <!-- Action Links -->
                <div class="grid grid-cols-2 gap-4">
                    <a href="javascript:void(0)" (click)="viewWaiver(waiver.code)"
                        class="flex items-center text-sm text-secondary-blue gap-2 underline">
                        <img src="/assets/icons/preview-pink.svg" alt="View" />
                        View Waiver
                    </a>

                    <a href="javascript:void(0)" (click)="downloadWaiver(waiver.code)"
                        class="flex items-center text-sm text-secondary-blue gap-2 underline">
                        <img src="/assets/icons/download-green.svg" alt="Download" />
                        Download Waiver
                    </a>
                </div>
            </div>
        </div>
        }
    </div>
</div>