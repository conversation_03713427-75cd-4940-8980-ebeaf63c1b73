import { CommonModule } from '@angular/common';
import { Component, signal } from '@angular/core';
import { AddMinorFormModalComponent } from '../add-minor-form-modal/add-minor-form-modal.component';
import { ModalComponent, PageFooterComponent } from 'lib-ui-kit';

interface Relation {
    type: 'primary' | 'minor';
    name: string;
    number?: number;
}
@Component({
    selector: 'app-my-relations',
    imports: [
        CommonModule,
        PageFooterComponent,
        ModalComponent,
        AddMinorFormModalComponent
    ],
    templateUrl: './my-relations.component.html',
    styleUrl: './my-relations.component.scss'
})
export class MyRelationsComponent {
    readonly showAddMinorForm = signal<boolean>(false);

    readonly primaryRelation = signal<Relation>({
        type: 'primary',
        name: '<PERSON><PERSON><PERSON>',
    });
    readonly minorRelations = signal<Relation[]>([
        {
            type: 'minor',
            name: 'Child 1',
            number: 1,
        },
        {
            type: 'minor',
            name: 'Child 2',
            number: 2,
        },
    ]);

} 
