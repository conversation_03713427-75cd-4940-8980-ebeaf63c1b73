<div class="min-h-[calc(100vh-168px)] bg-transparent md:bg-surface-white rounded-4xl">
    <!-- Main Content Container -->
    <div class="grid gap-5 md:gap-6 p-0 md:p-6">
        <h1 class="text-lg font-semibold text-primary">My Relations</h1>
        <!-- Primary Account Holder -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @if (primaryRelation()) {
            <div class="bg-surface-white rounded-3xl shadow-lg p-6 w-full">
                <div class="text-xs text-neutral-dark">
                    <span>Primary Account Holder</span>
                </div>
                <div class="text-primary font-medium text-sm">
                    {{ primaryRelation().name }}
                </div>
            </div>
            }
        </div>

        <!-- Minors Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @for (minor of minorRelations(); track $index) {
            <div class="bg-surface-white rounded-3xl shadow-lg p-6">
                <div class="text-xs text-neutral-dark">
                    <span>Minor {{ minor.number }}</span>
                </div>
                <div class="text-primary font-medium text-sm">
                    {{ minor.name }}
                </div>
            </div>
            }
        </div>

        <!-- Divider -->
        <div class="w-full border-b-2 border-surface opacity-30 hidden md:block"></div>

        <!-- Add Minor Button -->
        <div class="hidden md:flex md:items-center gap-3">
            <button (click)="showAddMinorForm.set(true)"
                class="flex items-center justify-center min-w-[300px] gap-2 border border-primary rounded-full px-6 py-3 text-center text-primary font-medium">
                <img src="/assets/icons/person-plus.svg" alt="Add Minor" />
                Add Minor
            </button>
        </div>
    </div>
</div>

<ng-template #addMinorForm> <app-add-minor-form-modal /> </ng-template>
<lib-modal [isOpen]="showAddMinorForm()" [modalContent]="addMinorForm" (closeModal)="showAddMinorForm.set(false)"
    dialogueHeader="Add Minor">
</lib-modal>

<!-- Footer CTA (Mobile) -->
<lib-page-footer>
    <!-- Add new card Button -->
    <button type="button" (click)="showAddMinorForm.set(true)"
        class="w-full py-2 px-4bg-surface-white border border-primary rounded-full text-primary font-medium flex items-center justify-center gap-2">
        <img src="assets/icons/person-plus.svg" alt="Image" />
        Add Minor
    </button>
</lib-page-footer>