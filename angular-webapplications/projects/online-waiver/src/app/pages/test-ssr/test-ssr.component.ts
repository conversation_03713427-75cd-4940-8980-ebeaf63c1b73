import { Component, Inject, PLA<PERSON>ORM_ID, OnInit, inject } from '@angular/core';
import { AppInitService } from '../../core/app-init.service';
// import { isPlatformServer, JsonPipe, NgIf } from '@angular/common';
// import { AppInitService } from 'lib-app-core';

@Component({
    selector: 'app-test-ssr',
    standalone: true,
    // imports: [NgIf, JsonPipe],
    templateUrl: './test-ssr.component.html',
    // styleUrls: ['./test-ssr.component.scss'],
    // template: `
    // <h1>Home Page</h1>
    // <p *ngIf="appDataService.data; else loading">
    //   <strong>Login Response:</strong> {{ appDataService.data.login | json }} <br>
    //   <strong>API 2:</strong> {{ appDataService.data.api2 | json }} <br>
    //   <strong>API 3:</strong> {{ appDataService.data.api3 | json }} <br>
    // </p>
    // <ng-template #loading><p>Loading data...</p></ng-template>`
})
export class TestSsrComponent {

    appDataService = inject(AppInitService);

}
