/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-09-18
 */

import { Component } from '@angular/core';
// import { AboutPageTheme } from '../../../../../common-config/theme-env';
import { SiteBaseComponent } from '../../core/sitebase.component';
import { AboutPageTheme } from '@theme/waiver/waiver.theme';
import { PageInterface, PARENT_FUNCTION_TOKEN } from 'lib-app-core';

@Component({
    standalone: true,
    selector: 'app-test-ui-components',
    imports: [],
    templateUrl: './test-ui-components.component.html',
    styleUrl: './test-ui-components.component.scss',
    providers: [
        {
            provide: PARENT_FUNCTION_TOKEN,
            useFactory: (parent: TestUiComponentsComponent) => {
                return parent.getPageTheme ? () => parent.getPageTheme() : () => { };
            },
            deps: [TestUiComponentsComponent] // Ensure that the parent component is resolved
        }
    ]
})
export class TestUiComponentsComponent extends SiteBaseComponent implements PageInterface {
    data: any; // This is undefined, trying to access `message` will cause a runtime error

	protected getRelevantTheme(){
		return AboutPageTheme;
	}

	getPageTheme() {
		return this.getRelevantTheme();
	}
}
