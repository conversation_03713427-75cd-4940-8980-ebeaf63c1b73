
<!-- <p>Message: {{ data.message }}</p> -->
<section class="py-20">
    <div class="mb-12">
        <h2 class="text-3xl border-b mb-6 pb-4">Primary</h2>
        <button type="button" class="{{getBtnPrimary()}}">Extra small</button>
        <button type="button" class="{{getBtnPrimary()}}">Small</button>
        <button type="button" class="{{getBtnPrimary()}}">Base</button>
        <button type="button" class="{{getBtnPrimary()}}">Large</button>
        <button type="button" class="{{getBtnPrimary()}}">Extra large</button>
    </div>

    <div class="mb-12">
        <h2 class="text-3xl border-b mb-6 pb-4">Primary Outline</h2>
        <button type="button" class="{{getBtnPrimaryStroked({size:'xs'})}}">Extra small</button>
        <button type="button" class="{{getBtnPrimaryStroked({size:'sm'})}}">Small</button>
        <button type="button" class="{{getBtnPrimaryStroked({size:'base'})}}">Base</button>
        <button type="button" class="{{getBtnPrimaryStroked({size:'lg'})}}">Large</button>
        <button type="button" class="{{getBtnPrimaryStroked({size:'xl'})}}">Extra large</button>
    </div>

    <div class="mb-12">
        <h2 class="text-3xl border-b mb-6 pb-4">Accent</h2>
        <button type="button" class="{{getBtnAccent()}}">Extra small</button>
        <button type="button" class="{{getBtnAccent()}}">Small</button>
        <button type="button" class="{{getBtnAccent()}}">Base</button>
        <button type="button" class="{{getBtnAccent()}}">Large</button>
        <button type="button" class="{{getBtnAccent()}}">Extra large</button>
    </div>

    <div class="mb-12">
        <h2 class="text-3xl border-b mb-6 pb-4">Accent Outline</h2>
        <button type="button" class="{{getBtnAccentStroked({size:'xs'})}}">Extra small</button>
        <button type="button" class="{{getBtnAccentStroked({size:'sm'})}}">Small</button>
        <button type="button" class="{{getBtnAccentStroked({size:'base'})}}">Base</button>
        <button type="button" class="{{getBtnAccentStroked({size:'lg'})}}">Large</button>
        <button type="button" class="{{getBtnAccentStroked({size:'xl'})}}">Extra large</button>
    </div>
</section>

<!-- <button type="button" class="px-3 py-2 text-xs font-medium text-center text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300">Extra small</button>
<button type="button" class="px-3 py-2 text-sm font-medium text-center text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300">Small</button>
<button type="button" class="px-5 py-2.5 text-sm font-medium text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 rounded-lg text-center">Base</button>
<button type="button" class="px-5 py-3 text-base font-medium text-center text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300">Large</button>
<button type="button" class="px-6 py-3.5 text-base font-medium text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 rounded-lg text-center">Extra large</button> -->

<!-- <button type="button"
    class="text-primary-500 bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm p-2.5 text-center inline-flex items-center me-2">
    <svg class="w-5 h-5 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 10">
        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M1 5h12m0 0L9 1m4 4L9 9" />
    </svg>
    <span class="sr-only">Icon description</span>
</button>

<button type="button"
    class="text-primary-700 hover:bg-primary-800 hover:text-white focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-full text-sm p-2.5 text-center inline-flex items-center me-2">
    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 10">
        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M1 5h12m0 0L9 1m4 4L9 9" />
    </svg>
    <span class="sr-only">Icon description</span>
</button>

<button type="button"
    class="text-primary-700 border border-primary-700 hover:bg-primary-700 hover:text-white focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm p-2.5 text-center inline-flex items-center me-2">
    <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 18 18">
        <path
            d="M3 7H1a1 1 0 0 0-1 1v8a2 2 0 0 0 4 0V8a1 1 0 0 0-1-1Zm12.954 0H12l1.558-4.5a1.778 1.778 0 0 0-3.331-1.06A24.859 24.859 0 0 1 6 6.8v9.586h.114C8.223 16.969 11.015 18 13.6 18c1.4 0 1.592-.526 1.88-1.317l2.354-7A2 2 0 0 0 15.954 7Z" />
    </svg>
    <span class="sr-only">Icon description</span>
</button>
<button type="button"
    class="text-primary-700 border border-primary-700 hover:bg-primary-700 hover:text-white focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-full text-sm p-2.5 text-center inline-flex items-center">
    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 18 18">
        <path
            d="M3 7H1a1 1 0 0 0-1 1v8a2 2 0 0 0 4 0V8a1 1 0 0 0-1-1Zm12.954 0H12l1.558-4.5a1.778 1.778 0 0 0-3.331-1.06A24.859 24.859 0 0 1 6 6.8v9.586h.114C8.223 16.969 11.015 18 13.6 18c1.4 0 1.592-.526 1.88-1.317l2.354-7A2 2 0 0 0 15.954 7Z" />
    </svg>
    <span class="sr-only">Icon description</span>
</button> -->