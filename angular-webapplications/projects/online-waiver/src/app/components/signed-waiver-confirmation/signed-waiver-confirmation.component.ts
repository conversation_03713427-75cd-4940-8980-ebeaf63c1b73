import { Component, input } from '@angular/core';

@Component({
    selector: 'app-signed-waiver-confirmation',
    imports: [],
    templateUrl: './signed-waiver-confirmation.component.html',
    styleUrl: './signed-waiver-confirmation.component.scss'
})
export class SignedWaiverConfirmationComponent {
    signInCode = input<string>();

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onDownloadWaiverDocument() { }

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onSignForMore() { }

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onViewSignedWaiver() { }

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onLogout() { }

}
