/**
 * @component AddParticipantsComponent
 * @description
 * Displays Primary and related customer details such as firstName, lastName, DOB and signed status 
 * Allows Adding the new minor and select the participants for signing
 * Also updates the component based on waiver signed and minors added 
 *
 * @usage
 * User will be routed to this page with CustomerId and SelectedWaiverSetId as a route params 
 *
 * @inputs
 * - CustomerId  : Logged in primary customer Id 
 * - SelectedWaiverSetId : The waiver set selected in landing page multi waiver scenario , if only one waiver is configured by default it is considered that ID 
 *
 * @outputs
 * - Redirects to sign waiver screen with selected list of customerId's
 *
 * @dependencies
 * - SignWaiverService: Handles API communication related to customer data.
 *
 * @methods
 * - initiateAddParticipants(): Communicates with api service and processes the user data for display.
 * - addMinor(): opens the add minor form to enter the minor details.
 * - saveMinor(): saves the values enetered in add minor form.
 * - signWaiver(): Redirects to sign waiver component with selected list of customerIds
 * 
 *  @Tobe_saved 
 *  waiverResp , signed waivers resp 
 */


import { Component, Signal, signal, viewChildren } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AddMinorFormComponent } from '../add-minor-form/add-minor-form.component';
import { TagComponent } from '../tag/tag.component';
import { CheckBoxComponent, PageFooterComponent } from 'lib-ui-kit';
import { Router } from '@angular/router';

interface Participant {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    type: 'primary' | 'minor';
}

@Component({
    selector: 'app-add-participants',
    imports: [
        ReactiveFormsModule,
        FormsModule,
        TagComponent,
        AddMinorFormComponent,
        PageFooterComponent,
        CheckBoxComponent
    ],
    templateUrl: './add-participants.component.html',
    styleUrl: './add-participants.component.scss'
})
export class AddParticipantsComponent {
    readonly signInCode = signal<string>('SJ5FK0');
    selectedParticipants = signal<Participant[]>([]);
    minorFormComponents: Signal<readonly AddMinorFormComponent[]> = viewChildren(
        AddMinorFormComponent
    );

    constructor(private router: Router) {

    }

    readonly showLoginSuccess = signal<boolean>(false);

    // These are the default participants that are displayed in the add participants page, which can be removed once the API is integrated
    primaryParticipant: Participant = {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '25-03-1991',
        type: 'primary',
    };

    minorParticipants: Participant[] = [
        {
            firstName: 'Robert',
            lastName: 'Frox',
            dateOfBirth: '15-01-2009',
            type: 'minor',
        },
        {
            firstName: 'Alice',
            lastName: 'Doe',
            dateOfBirth: '12-11-2011',
            type: 'minor',
        },
    ];

    minorErrorMessages = {
        firstName: 'First name is required',
        lastName: 'Last name is required',
        dateOfBirth: 'Date of birth is required',
    };

    signWaiver() {
        console.log('Selected participants', this.selectedParticipants());
        const minorFormData = this.minorFormComponents().map(minorFormComponent =>
            minorFormComponent.getFormData()
        );
        this.router.navigate(['/waivers/sign-waiver/53/1'])
    }

    addMinor(): void {
        this.minorParticipants.push({
            firstName: '',
            lastName: '',
            dateOfBirth: '',
            type: 'minor',
        });
    }

    removeMinor(index: number): void {
        this.minorParticipants.splice(index, 1);
    }

    isMinorValid(minor: Participant) {
        return (
            minor.firstName &&
            minor.lastName &&
            minor.dateOfBirth &&
            minor.type === 'minor'
        );
    }

    /**
     * This function is called when the checkbox is changed.
     * It adds or removes the participant from the selected participants list.
     * @param participant - The participant that is being selected or deselected.
     * @param event - The event that is being triggered.
     */
    onCheckboxChange(participant: Participant, event: Event): void {
        const checkbox = event.target as HTMLInputElement;
        if (checkbox.checked) {
            this.selectedParticipants.update(prev => [...prev, participant]);
        } else {
            this.selectedParticipants.update(prev =>
                prev.filter(p => p !== participant)
            );
        }
    }
}
