<div class="grid gap-6 max-w-7xl mx-auto mb-32 md:mb-0">
    <h1 class="text-lg font-semibold">Sign waiver - Rollercoaster23</h1>

    <!-- Participants Section -->
    <section class="bg-surface-white rounded-4xl p-2 md:p-6 grid gap-2 md:gap-6">
        <h2 class="text-lg font-medium text-primary px-3 pt-3 md:p-0 mb-1.5 md:mb-0">
            Select members to sign
        </h2>

        <!-- Primary Account Holder -->
        <section class="p-5 bg-surface-lightest rounded-3xl flex flex-col gap-2">
            <h3 class="text-sm">Primary account holder</h3>
            <div class="flex items-center justify-between pr-[11px]">
                <div class="flex items-center justify-center gap-3">
                    <lib-checkbox customClass="w-[18px] h-[18px]"
                        (change)="onCheckboxChange(primaryParticipant, $event)" />
                    <div class="flex flex-col">
                        <p class="text-sm">
                            {{
                            primaryParticipant.firstName + ' ' + primaryParticipant.lastName
                            }}
                        </p>
                        <div class="text-sm text-neutral-dark">
                            {{ primaryParticipant.dateOfBirth }}
                        </div>
                    </div>
                </div>
                <app-tag [label]="'Signed'" [severity]="'success'" />
            </div>
        </section>

        <!-- Minor Participants -->
        <section class="p-5 bg-surface-lightest rounded-3xl flex flex-col gap-5">
            <div class="grid gap-2">
                <div class="flex flex-col gap-2">
                    <h3 class="text-sm">Minors</h3>
                    <p class="text-sm text-neutral-dark">
                        Minors info should be added in order to selecting them for signing.
                    </p>
                </div>
                <div class="grid gap-2">
                    @for (minor of minorParticipants; track $index) {
                    @if (isMinorValid(minor)) {
                    <div class="flex items-center justify-between">
                        <div class="flex items-center justify-center gap-3">
                            <lib-checkbox customClass="w-[18px] h-[18px]" (change)="onCheckboxChange(minor, $event)" />
                            <div class="flex flex-col">
                                <p class="text-sm">
                                    {{ minor.firstName + ' ' + minor.lastName }}
                                </p>
                                <p class="text-sm text-neutral-dark">
                                    {{ minor.dateOfBirth }}
                                </p>
                            </div>
                        </div>
                        <app-tag [label]="'Not Signed'" [severity]="'error'" />
                    </div>
                    @if (minorParticipants.length - 1 !== $index) {
                    <hr class="text-surface hidden md:block" />
                    }
                    }
                    }
                </div>
            </div>

            <!-- Invalid Minors -->
            @for (minorForm of minorParticipants; track $index) {
            @if (!isMinorValid(minorForm)) {
            <app-add-minor-form [index]="$index" [errorMessages]="minorErrorMessages"
                (remove)="removeMinor($index)"></app-add-minor-form>
            }
            }

            <!-- Add Minor Button -->
            <button type="button" (click)="addMinor()"
                class="w-full md:w-[30%] py-2 px-4 mb-6 bg-transparent border border-primary rounded-full text-primary font-medium flex items-center justify-center gap-2">
                <img src="assets/icons/person-plus.svg" alt="Image" />
                Add Minor
            </button>
        </section>

        <hr class="text-surface hidden md:block" />

        <!-- Desktop CTA -->
        <div class="hidden md:flex flex-col md:flex-row md:items-center gap-4">
            <!-- Login Button -->
            <button
                class="w-full max-w-[23rem] py-3 px-4 bg-primary text-white font-medium rounded-4xl disabled:bg-surface"
                (click)="signWaiver()">
                Sign Waiver
            </button>
            <div class="text-sm text-center md:text-right">
                Already have an account?
                <a href="#" class="text-blue-600 underline">Login</a>
            </div>
        </div>
    </section>
</div>

<!-- Footer CTA (Mobile) -->
<lib-page-footer>
    <div class="flex flex-col justify-center items-center gap-4">
        <!-- Login Button -->
        <button class="w-full max-w-[23rem] py-3 px-4 bg-primary text-white font-medium rounded-4xl disabled:bg-surface"
            (click)="signWaiver()">
            Sign Waiver
        </button>
        <div class="text-sm text-center md:text-right">
            Already have an account?
            <a href="#" class="text-blue-600 underline">Login</a>
        </div>
    </div>
</lib-page-footer>