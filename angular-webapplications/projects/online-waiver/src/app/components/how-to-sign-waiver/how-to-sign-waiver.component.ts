import { Component } from '@angular/core';
export interface WaiverStep {
    step: number;
    title: string;
    description: string;
}
@Component({
    selector: 'app-how-to-sign-waiver',
    imports: [],
    templateUrl: './how-to-sign-waiver.component.html',
    styleUrl: './how-to-sign-waiver.component.scss'
})
export class HowToSignWaiverComponent {
    waiverSteps: WaiverStep[] = [
        {
            step: 1,
            title: 'Identify yourself',
            description:
                'Log in to your existing account or create a new one to identify yourself.',
        },
        {
            step: 2,
            title: 'Select members to sign',
            description:
                'Add details of minors and select the members who needs to sign.',
        },
        {
            step: 3,
            title: 'Read and sign waiver',
            description:
                'Read the document carefully and provide your sign at the end of the waiver document.',
        },
    ];
}
