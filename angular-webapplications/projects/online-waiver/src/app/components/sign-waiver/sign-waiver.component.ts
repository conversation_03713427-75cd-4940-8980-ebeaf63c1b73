/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-09-18
 */

import { Component, input, signal } from '@angular/core';
import { SignedWaiverConfirmationComponent } from '../signed-waiver-confirmation/signed-waiver-confirmation.component';
import { ModalComponent } from 'lib-ui-kit';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

@Component({
    selector: 'app-sign-waiver',
    standalone: true,
    imports: [
        SignedWaiverConfirmationComponent,
        ModalComponent,
        CommonModule
    ],
    templateUrl: './sign-waiver.component.html',
    styleUrl: './sign-waiver.component.scss'
})
export class SignWaiverComponent {
    participants = input<string[]>(['Robert', 'Esther']);
    readonly signInCode = signal<string>('SJ5FK0');
    readonly showLoginSuccess = signal<boolean>(false);

    constructor(private router: Router) {

    }

    getNumberOfParticipants() {
        return this.participants().length.toString().padStart(2, '0');
    }

    addOrRemoveParticipants() {
        this.router.navigate(['/waivers/add-participants/53/1']);
    }

    signWaiver() {
        this.showLoginSuccess.set(true);
    }
}
