<div class="mx-auto max-w-7xl">
    <h1 class="text-lg font-semibold mb-5 md:mb-6">
        Sign waiver - Rollercoaster23
    </h1>

    <!-- Participants Info -->
    <div class="bg-surface-white rounded-4xl shadow-lg p-5 md:p-6 mb-5 md:mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="mr-3">
                <h3 class="text-lg font-medium text-primary">
                    Participants ({{ getNumberOfParticipants() }})
                </h3>
                <p class="text-sm text-neutral-dark mt-1 md:max-w-xl truncate overflow-ellipsis">
                    {{ participants().join(', ') }}
                </p>
            </div>
            <div class="mt-2 md:mt-0">
                <a href="#" class="text-sm text-secondary-blue underline">
                    Add or remove participants
                </a>
            </div>
        </div>
    </div>

    <!-- Waiver form -->
    <div class="bg-surface-white rounded-4xl shadow-lg p-5 md:p-6">
        <div>
            <h2 class="text-lg leading-6 font-medium text-primary">
                Read and sign waiver
            </h2>
            <p class="text-sm text-neutral-dark mt-1">
                Please read the waiver carefully and sign at the end of the document.
            </p>
        </div>

        <!-- Waiver Document (Scrollable) -->
        <div class="mt-5 md:mt-6 bg-surface-lighter rounded-4xl w-full overflow-hidden">
            <iframe class="min-h-[70vh] md:min-h-[80vh] w-full object-contain flex justify-center"
                src="/assets/documents/semnox.pdf#toolbar=0&navpanes=0&zoom=100"></iframe>
        </div>

        <div class="flex flex-col justify-center items-center gap-4 mt-4">
            <button
                class="w-full max-w-[23rem] py-3 px-4 bg-primary text-white font-medium rounded-4xl disabled:bg-surface"
                (click)="signWaiver()">
                Sign Waiver
            </button>
        </div>
    </div>
</div>

<!-- Login Success Modal -->
<ng-template #loginSuccess>
    <app-signed-waiver-confirmation [signInCode]="signInCode()" />
</ng-template>

<lib-modal [isOpen]="showLoginSuccess()" [modalContent]="loginSuccess" (closeModal)="showLoginSuccess.set(false)">
</lib-modal>