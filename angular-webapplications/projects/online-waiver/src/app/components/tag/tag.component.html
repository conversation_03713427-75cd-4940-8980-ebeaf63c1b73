<div class="rounded-3xl flex gap-1 items-center justify-center py-1 px-2 text-xs w-max" [ngClass]="{
    'bg-feedback-surface-success text-feedback-success ':
      severity() === 'success',
    'bg-feedback-surface-error text-feedback-error': severity() === 'error',
    'bg-feedback-surface-warning text-feedback-warning':
      severity() === 'warning',
    'bg-feedback-surface-info text-feedback-info': severity() === 'info',
  }">
    <img [src]="IconPath" [alt]="severity()" class="w-4 h-4" />
    <span>{{ label() }}</span>
</div>