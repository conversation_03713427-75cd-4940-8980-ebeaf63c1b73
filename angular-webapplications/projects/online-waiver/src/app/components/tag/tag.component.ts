import { CommonModule } from '@angular/common';
import { Component, input } from '@angular/core';
export type Severity = 'success' | 'warning' | 'error' | 'info';

@Component({
    selector: 'app-tag',
    imports: [CommonModule],
    templateUrl: './tag.component.html',
    styleUrl: './tag.component.scss'
})
export class TagComponent {
    severity = input.required<Severity>();
    label = input.required<string>();

    get IconPath() {
        switch (this.severity()) {
            case 'error':
                return 'assets/icons/close-red.svg';
            case 'success':
                return 'assets/icons/green-tick.svg';
            case 'warning':
                return 'assets/icons/warning.svg';
            case 'info':
                return 'assets/icons/info.svg';
        }
    }
}
