<div id="add-minor" class="mt-8">

    <div
        class="flex items-center justify-between w-full p-4 font-medium rtl:text-right text-gray-500 border border-b-0 border-gray-200 rounded-t-xl focus:ring-4 hover:bg-primary-300 gap-3 bg-primary-200">

        <span class="font-semibold text-sm">Minor 1</span>

        <button type="button"
            class="text-gray-700 hover:bg-gray-800 hover:text-white focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-full text-sm p-2 text-center inline-flex items-center">
            <svg class="w-5 h-5w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
            </svg>
            <span class="sr-only">Icon description</span>
        </button>
    </div>

    <div id="accordion-collapse-body-1" class="" aria-labelledby="accordion-collapse-heading-1">
        <div class="p-5 border border-gray-200 rounded-b-xl">
            <div class="grid grid-cols-1 gap-4">
                <div>
                    <label class="text-gray-800 text-sm mb-2 block">First Name</label>
                    <div class="relative flex items-center">
                        <input name="name" type="text" required
                            class="text-gray-800 bg-white border border-gray-100 w-full text-sm px-4 py-2.5 rounded-xl outline-gray-200"
                            placeholder="First name" />
                    </div>
                </div>

                <div>
                    <label class="text-gray-800 text-sm mb-2 block">Last Name</label>
                    <div class="relative flex items-center">
                        <input name="email" type="email" required
                            class="text-gray-800 bg-white border border-gray-100 w-full text-sm px-4 py-2.5 rounded-xl outline-gray-200"
                            placeholder="Last name" />
                    </div>
                </div>

                <div>
                    <label class="text-gray-800 text-sm mb-2 block">DOB</label>
                    <div class="relative flex items-center">
                        <input name="password" type="password" required
                            class="text-gray-800 bg-white border border-gray-100 w-full text-sm px-4 py-2.5 rounded-xl outline-gray-200"
                            placeholder="mm/dd/yyyy" />
                    </div>
                </div>
            </div>
        </div>
    </div>

    <button type="button" class="{{getBtnAccent()}} mt-6">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
            class="size-4 inline">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
        </svg>
        Add Minor
    </button>

</div>