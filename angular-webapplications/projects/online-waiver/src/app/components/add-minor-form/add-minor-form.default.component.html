<div class="mb-6 p-5 md:p-6 shadow-surround-lg rounded-3xl bg-surface-white">
    <div class="flex items-center justify-between mb-4">
        <h3 class="font-semibold text-sm">Minor {{ index() + 1 }}</h3>
        <button type="button" (click)="removeMinor()" aria-label="Remove minor">
            <img src="/assets/icons/remove.svg" alt="Remove" />
        </button>
    </div>

    <form [formGroup]="minorForm">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- First Name -->
            <lib-text-input formControlName="firstName" name="minor-{{ index() }}-firstName" label="First Name"
                placeholder="Enter first name" [required]="true"
                [errorMessage]="getErrorMessage('firstName')"></lib-text-input>

            <!-- Last Name -->
            <lib-text-input formControlName="lastName" name="minor-{{ index() }}-lastName" label="Last Name"
                placeholder="Enter last name" [required]="true"
                [errorMessage]="getErrorMessage('lastName')"></lib-text-input>

            <!-- Date of Birth -->
            <lib-date-picker formControlName="dateOfBirth" name="minor-{{ index() }}-dateOfBirth" label="Date of Birth"
                placeholder="DD-MM-YYYY" [required]="true" [rightOffset]="0"
                [errorMessage]="getErrorMessage('dateOfBirth')" [position]="'top'"></lib-date-picker>
        </div>
    </form>
</div>