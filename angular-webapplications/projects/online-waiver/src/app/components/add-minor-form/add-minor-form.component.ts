/**
 * @fileoverview Add Minor Form Component
 * 
 * This component is responsible for rendering the "Add Minor" form, 
 * applying the appropriate theme configuration, and optionally inheriting 
 * a theme from the parent component.
 * 
 * <AUTHOR>
 * @version 1.0.1
 * @created 2024-09-18
 */

import { Component, Inject, input, Optional, output } from '@angular/core';
import { PARENT_FUNCTION_TOKEN, getTemplateUrl } from 'lib-app-core';
import { SiteBaseComponent } from '../../core/sitebase.component';
import { AddMinorFormTheme } from '@theme/waiver/waiver.theme';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { DatePickerComponent, TextInputComponent } from 'lib-ui-kit';

interface MinorFormData {
    firstName: FormControl<string | null>;
    lastName: FormControl<string | null>;
    dateOfBirth: FormControl<string | null>;
}


@Component({
    standalone: true,
    selector: 'app-add-minor-form',
    imports: [
        CommonModule,
        ReactiveFormsModule,
        TextInputComponent,
        DatePickerComponent,
    ],
    templateUrl: getTemplateUrl(AddMinorFormTheme, 'add-minor-form'),
    styleUrls: ['./add-minor-form.component.scss'] // Ensuring correct array format
})
export class AddMinorFormComponent extends SiteBaseComponent {

    /**
     * Stores the inherited theme from the parent component, if available.
     */
    parentTheme: Record<string, any> = {};

    constructor(
        /**
         * Injects an optional function that provides the parent component's theme.
         * If the parent does not provide this function, the component falls back to its own theme.
         */
        @Optional() @Inject(PARENT_FUNCTION_TOKEN) private getPageTheme: (() => Record<string, any>) | null,
        private fb: FormBuilder
    ) {
        super();

        // If a parent theme function is provided, retrieve the theme from the parent
        this.parentTheme = this.getPageTheme?.() ?? {};
        this.minorForm = this.fb.group({
            firstName: ['', Validators.required],
            lastName: ['', Validators.required],
            dateOfBirth: ['', Validators.required],
        });
    }

    /**
     * Retrieves the relevant theme for this component.
     * - Merges the parent theme (if available) with the local `AddMinorFormTheme`.
     * - Ensures a fallback to an empty object if `getPageTheme` is not provided.
     * 
     * @returns A merged theme object with parent theme properties and local theme properties.
     */
    protected getRelevantTheme(): Record<string, any> {
        return { ...this.parentTheme, ...AddMinorFormTheme };
    }

    index = input<number>(0);
    errorMessages = input<Record<string, string>>({});
    remove = output<number>();

    minorForm: FormGroup;

    removeMinor(): void {
        this.remove.emit(this.index());
    }

    // Method to get form data
    getFormData(): MinorFormData {
        return this.minorForm.value;
    }

    // Method to check if form is valid
    isValid(): boolean {
        return this.minorForm.valid;
    }

    // Method to mark all fields as touched (for validation)
    markAsTouched(): void {
        this.minorForm.markAllAsTouched();
    }

    getErrorMessage(fieldName: string): string | null {
        const control = this.minorForm.get(fieldName);

        if (control?.invalid && control?.touched && control.errors) {
            return this.errorMessages()[fieldName] || null;
        }

        return null;
    }
}
