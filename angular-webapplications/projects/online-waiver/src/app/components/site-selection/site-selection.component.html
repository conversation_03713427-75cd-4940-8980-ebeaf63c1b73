<div
    class="max-w-[682px] mx-auto bg-surface-white rounded-4xl p-5 md:p-6 flex flex-col gap-5 md:gap-6"
>
    <h1 class="font-semibold text-lg">Select your preferred location</h1>

    <lib-text-input
        placeholder="Search"
        [icon]="
            searchTerm()
                ? 'assets/icons/close-black.svg'
                : 'assets/icons/magnifier.svg'
        "
        [(ngModel)]="searchTerm"
        [onIconClick]="clearSearchTerm()"
    />

    <div class="grid gap-5 md:gap-6 place-items-center" [class]="customClass">
        @for (site of filteredSites(); track $index) {
        <button
            [tabIndex]="0"
            class="rounded-3xl bg-surface-lightest p-2 flex items-center gap-5 shadow-lg cursor-pointer transition-colors w-full md:min-w-[305px]"
            (click)="siteViewsServiceBL.selectSite(site.SiteId)"
        >
            <img
                libImagePlaceholder
                [src]="site.Logo"
                [alt]="site.SiteName"
                class="object-cover rounded-3xl h-[90px] w-[90px]"
            />
            <div class="flex flex-col gap-1 text-left">
                <p class="font-medium text-sm">{{ site.SiteName }}</p>
                <p class="text-neutral-dark text-xs">
                    {{ site.SiteAddress }}
                </p>
            </div>
        </button>

        } @empty {
        <div class="grid gap-5 md:gap-6 place-items-center">
            <img src="assets/icons/magnifier-L.svg" alt="No results" />

            <p class="font-medium text-lg">No results found</p>

            <p class="text-neutral-dark text-sm text-center px-8">
                The location you entered is invalid, incorrect, or currently
                outside our service area. Please try again.
            </p>
        </div>
        }
    </div>
</div>
