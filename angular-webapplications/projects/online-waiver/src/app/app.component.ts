/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-09-18
 */

import { Component, inject, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { AppInitService } from './core/app-init.service';
import { HeaderComponent } from './shared/header/header.component';

@Component({
    standalone: true,
    selector: 'app-root',
    imports: [RouterOutlet, HeaderComponent],
    templateUrl: './app.component.html',
    styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit {
    title = 'online-waiver';
    private readonly appInitService = inject(AppInitService);

    async ngOnInit() {
        try {
            await this.appInitService.handleSiteInitialization();
        } catch (error) {
            console.error('Failed to initialize site:', error);
        }
    }
}
