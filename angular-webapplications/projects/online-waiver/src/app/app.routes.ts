/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-09-18
 */

import { Routes } from '@angular/router';
import { LandingPageComponent } from './pages/landing/landing.page.component';
import { TestUiComponentsComponent } from './pages/test-ui-components/test-ui-components.component';
import { TestSsrComponent } from './pages/test-ssr/test-ssr.component';
import { PageNotFoundComponent } from 'lib-app-core';
import { SignWaiverComponent } from './components/sign-waiver/sign-waiver.component';
import { AddParticipantsComponent } from './components/add-participants/add-participants.component';
import { MySignedWaiversComponent } from './pages/my-account/my-signed-waivers/my-signed-waivers.component';
import { MyRelationsComponent } from './pages/my-account/my-relations/my-relations.component';
import { MainLayoutComponent } from './shared/main-layout/main-layout.component';
import { ProfileComponent } from 'lib-my-account';
import { SiteSelectionComponent } from './components/site-selection/site-selection.component';
import { PartyReservationMainComponent, ReservationBookingFormComponent } from 'party-reservation';
// import { TestPage2Component } from './pages/test-page-2/test-page-2.component';

export const routes: Routes = [
 
    { path: '', component: LandingPageComponent },
    {
        path: 'waivers/add-participants/:custId/:waiverSetId', component: AddParticipantsComponent, data: { breadcrumb: 'Add Participants' }
    },
    {
        path: 'waivers/sign-waiver/:custId/:waiverSetId', component: SignWaiverComponent, data: { breadcrumb: 'Sign Waiver' }
    },
    {
        path: 'home',
        redirectTo: '/waivers',
        pathMatch: 'full',
    },
    {
        path: 'my-accounts',
        component: MainLayoutComponent,
        data: { breadcrumb: 'My Accounts' },
        children: [
          {
            path: 'my-profile',
            component: ProfileComponent,
            data: { breadcrumb: 'My Profile' },
          },
          {
            path: '**',
            redirectTo: '/my-accounts/my-profile',
          },
        ],
    },
    {
        path: 'waivers',
        component: MainLayoutComponent,
        data: { breadCrumb: 'Waivers' },
        children: [
            {
                path: 'my-signed-waivers',
                component: MySignedWaiversComponent,
                data: { breadcrumb: 'My Signed Waivers' }
            },
            {
                path: 'my-relations',
                component: MyRelationsComponent,
                data: { breadcrumb: 'My Relations' }
            },
            { path: '**', redirectTo: '/waivers/my-signed-waivers' }
        ]
    },
    {
        path: 'reservation',
        component: MainLayoutComponent,
        data: { breadcrumb: 'Reservations' },
        children: [
            {
                path: '',
                component: PartyReservationMainComponent,
                data: { breadcrumb: 'Party Reservations' }
            },
            {
                path: 'book',
                component: ReservationBookingFormComponent,
                data: { breadcrumb: 'Book Reservation' }
            }
        ]
    },
    { path: 'site-selection', component: SiteSelectionComponent },
    { path: 'ui-components', component: TestUiComponentsComponent },
    { path: 'test-ssr', component: TestSsrComponent },
    { path: '**', component: PageNotFoundComponent }
];


