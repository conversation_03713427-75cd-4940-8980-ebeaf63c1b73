/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-02
 */
import { APP_BOOTSTRAP_LISTENER, ApplicationConfig, ErrorHandler, importProvidersFrom, inject, provideZoneChangeDetection } from '@angular/core';
import { provideRouter } from '@angular/router';

import { routes } from './app.routes';
import { provideClientHydration, withEventReplay } from '@angular/platform-browser';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { ExecutionContextInterceptor } from './core/executionContextInterceptor';
import { AppInitService } from './core/app-init.service';
import { DEFAULT_APP_CONFIG_TOKEN, APP_CONSTANTS_TOKEN, GlobalExceptionHandler } from 'lib-app-core';
import { defaultAppConfig } from '../../config/default-app.config'
import { waiverConstants } from './constants/waiver.constant';
// import { APP_ENVIRONMENT, GlobalExceptionHandler } from 'lib-app-core';

export const appConfig: ApplicationConfig = {
    providers: [
        provideZoneChangeDetection({ eventCoalescing: true }),
        provideRouter(routes),
        provideClientHydration(withEventReplay()),
        provideHttpClient(withInterceptorsFromDi()),
        { provide: ErrorHandler, useClass: GlobalExceptionHandler },
        // importProvidersFrom(HttpClientModule),
        { provide: HTTP_INTERCEPTORS, useClass: ExecutionContextInterceptor, multi: true },
        // { provide: APP_ENVIRONMENT, useValue: environment },
        { provide: DEFAULT_APP_CONFIG_TOKEN, useValue: defaultAppConfig },
        { provide: APP_CONSTANTS_TOKEN, useValue: waiverConstants },
    
        // {
        //     provide: APP_BOOTSTRAP_LISTENER,
        //     useFactory: () => {
        //         const appInitService = inject(AppInitService);
        //         return async () => {
        //             await appInitService.loadInitialData(); // 🟢 Call multiple APIs before bootstrapping
        //         };
        //     },
        //     multi: true,
        // },
    ]
};
