export interface WaiverSetContainerResponseFromAPI {
    data: WaiverSetContainerResponse;
}

export interface WaiverSetContainerResponse {
    WaiverSetContainerDTOList: WaiverSetContainerDTOList[];
    Hash: string;
}

export interface WaiverSetContainerDTOList {
    WaiverSetId: number;
    Name: string;
    WaiversContainerDTO: WaiversContainerDTO[];
    WaiverSetSigningOptionsContainerDTO: WaiverSetSigningOptionsContainerDTO[];
}

export interface WaiverSetSigningOptionsContainerDTO {
    LookupValueId: number;
    OptionName: string;
    OptionDescription: string;
}

export interface WaiversContainerDTO {
    Name: string;
    WaiverFileName: string;
    ValidForDays: null;
    EffectiveDate: Date;
    EffectiveTillDate: Date | null;
    WaiverSetDetailId: number;
    WaiverSetId: number;
    WaiverHTML: string;
    WaiverPdfBase64: null | string;
}