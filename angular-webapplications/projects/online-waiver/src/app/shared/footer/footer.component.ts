/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-09-18
 */

import { Component } from '@angular/core';
import { getTemplateUrl } from 'lib-app-core';
import { FooterTheme } from '@theme/waiver/waiver.theme';
import { SiteBaseComponent } from '../../core/sitebase.component';

@Component({
    standalone: true,
    selector: 'app-footer',
    imports: [],
    // templateUrl: './footer.default.component.html',
    templateUrl: getTemplateUrl(FooterTheme, 'footer'),
    styleUrl: './footer.component.scss'
})
export class FooterComponent extends SiteBaseComponent {
	protected getRelevantTheme() {
		return FooterTheme;
	}
}
