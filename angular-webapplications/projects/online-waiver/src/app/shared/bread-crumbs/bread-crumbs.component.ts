import { CommonModule } from '@angular/common';
import { Component, input } from '@angular/core';
import { RouterLink } from '@angular/router';
export interface Breadcrumb {
    label: string;
    link?: string;
}
@Component({
    selector: 'app-bread-crumbs',
    imports: [CommonModule, RouterLink],
    templateUrl: './bread-crumbs.component.html',
    styleUrl: './bread-crumbs.component.scss'
})
export class BreadCrumbsComponent {
    items = input<Breadcrumb[]>([]);
}
