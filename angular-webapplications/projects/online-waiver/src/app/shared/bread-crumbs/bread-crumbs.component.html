<nav aria-label="Breadcrumb" class="mb-5 lg:mb-6">
    <ol class="flex items-center flex-wrap space-x-2 text-sm text-neutral-dark">
        @for (item of items(); track $index) {
        <ng-container>
            <li>
                @if ($index !== items().length - 1 && item.link) {
                <a [routerLink]="item.link" class="hover:text-secondary-blue hover:underline">
                    {{ item.label }}
                </a>
                } @else if ($index !== items().length - 1 && !item.link) {
                <span>{{ item.label }}</span>
                } @else {
                <span class="font-medium text-primary">{{ item.label }}</span>
                }
            </li>
            @if ($index !== items().length - 1) {
            <li class="text-neutral-light">
                <img src="/assets/icons/chevron-right.svg" alt=">" class="w-3 h-3" />
            </li>
            }
        </ng-container>
        }
    </ol>
</nav>