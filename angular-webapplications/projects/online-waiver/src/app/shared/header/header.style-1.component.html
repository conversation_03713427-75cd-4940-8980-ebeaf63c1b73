<header class="sticky top-0 w-full bg-surface-white drop-shadow-sm z-50 lg:z-40">
    <!-- Desktop Header -->
    <div class="hidden lg:flex justify-between gap-6 items-center px-10 py-4 w-full">
        <div class="flex items-center gap-6">
            <a routerLink="/" class="flex items-center">
                <img src="assets/icons/semnox-logo.svg" alt="Semnox Logo" class="min-w-[101px]" />
            </a>

            <!-- Search Bar -->
            <div class="max-w-52 relative">
                <lib-text-input placeholder="Search" [icon]="'/assets/icons/magnifier.svg'"
                    [iconAlt]="'Magnifier'"></lib-text-input>
            </div>
        </div>

        <div class="flex items-center justify-center space-x-6">
            <nav class="flex items-center justify-between space-x-6">
                @for (item of navItems; track $index) {
                <ng-container>
                    <a [routerLink]="item.link" routerLinkActive="text-secondary-blue font-medium"
                        class="flex items-center gap-2 text-xs xl:text-sm text-primary hover:text-secondary-blue">
                        <img [src]="item.icon" [alt]="item.label" class="w-4 h-4 xl:w-5 xl:h-5" />
                        <span> {{ item.label }}</span>
                    </a>
                </ng-container>
                }
            </nav>

            <!-- My Account -->
            <div class="relative">
                <button class="flex items-center text-xs xl:text-sm text-primary hover:text-secondary-blue"
                    (click)="toggleAccountDropdown()">
                    <img src="/assets/icons/account.svg" alt="Account" class="w-4 h-4 xl:w-5 xl:h-5 mr-2" />
                    <span class="text-nowrap"> My Account</span>
                    <img src="/assets/icons/chevron-down.svg" alt="Dropdown" class="w-4 h-4 xl:w-5 xl:h-5 ml-2"
                        [class.transform]="accountDropdown" [class.rotate-180]="accountDropdown" />
                </button>

                <app-dropdown-menu [items]="accountMenuItems" [isOpen]="accountDropdown" [alignRight]="true"
                    (closeMenu)="accountDropdown = false"></app-dropdown-menu>
            </div>

            <!-- Shopping Cart -->
            <a href="#" class="text-primary">
                <img src="/assets/icons/cart.svg" alt="Cart" class="w-5 h-5 xl:w-6 xl:h-6 mx-6 xl:mx-0" />
            </a>

            <!-- Language Selector -->
            <div class="relative">
                <button class="flex items-center justify-center gap-1 px-2 py-1 rounded text-xs xl:text-sm"
                    (click)="toggleLanguageDropdown()">
                    <img src="/assets/icons/language.svg " class="w-5 h-5 xl:w-6 xl:h-6" alt="language" />
                    <span> {{ selectedLanguage.code }}</span>
                    <img src="/assets/icons/chevron-down.svg " alt="Dropdown" class="w-4 h-4"
                        [class.transform]="languageDropdown" [class.rotate-180]="languageDropdown" />
                </button>

                <app-dropdown-menu [items]="languageMenuItems" [isOpen]="languageDropdown" [alignRight]="true"
                    (closeMenu)="languageDropdown = false" [size]="'sm'"></app-dropdown-menu>
            </div>
        </div>
    </div>

    <!-- Mobile Header -->
    <div class="lg:hidden flex items-center justify-between px-6 pr-4 py-4">
        <div class="flex items-center gap-5">
            <button class="text-primary focus:outline-none min-h-6 min-w-6"
                (click)="toggleMobileSidebar(); $event.stopPropagation()" aria-label="Toggle menu">
                @if (!mobileSidebarOpen) {
                <img src="assets/icons/hamburger.svg" alt="open menu" />
                } @else {
                <img src="/assets/icons/menu-close.svg" alt="close menu" />
                }
            </button>

            <a routerLink="/" class="flex items-center">
                <img src="assets/icons/semnox-logo.svg" alt="Semnox Logo" />
            </a>
        </div>

        <div class="flex items-center gap-3">
            <!-- Shopping Cart -->
            <a href="#" class="text-primary">
                <img src="/assets/icons/cart.svg" alt="Cart" class="w-6 h-6" />
            </a>

            <!-- Language Selector -->
            <div class="relative">
                <button class="flex items-center gap-1 px-2 py-1 rounded text-xs xl:text-sm"
                    (click)="toggleLanguageDropdown()">
                    <img src="/assets/icons/language.svg " alt="language" />
                    <span class="min-w-6"> {{ selectedLanguage.code }}</span>
                    <img src="/assets/icons/chevron-down.svg " alt="Dropdown" class="w-4 h-4"
                        [class.transform]="languageDropdown" [class.rotate-180]="languageDropdown" />
                </button>

                <app-dropdown-menu [items]="languageMenuItems" [isOpen]="languageDropdown" [alignRight]="true"
                    (closeMenu)="languageDropdown = false"></app-dropdown-menu>
            </div>
        </div>
    </div>
</header>

<!-- Mobile Sidebar -->

<app-mobile-sidebar [isOpen]="mobileSidebarOpen" (closeSidebar)="closeSideBar()"></app-mobile-sidebar>