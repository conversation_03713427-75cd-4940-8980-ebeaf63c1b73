<app-client-header></app-client-header>
<header class="sticky top-0 w-full bg-surface-white drop-shadow-sm z-50 lg:z-40">
    <div class="flex justify-center items-center px-6 lg:px-10 py-4 w-full">
        <div class="flex items-center gap-5">
            <button class="lg:hidden text-primary min-h-6 min-w-6"
                (click)="toggleMobileSidebar(); $event.stopPropagation()" aria-label="Toggle menu">
                @if (!mobileSidebarOpen) {
                <img src="assets/icons/hamburger.svg" alt="open menu" />
                } @else {
                <img src="/assets/icons/menu-close.svg" alt="close menu" />
                }
            </button>

            <!-- <a routerLink="/" class="flex items-center">
                <img src="assets/icons/semnox-logo.svg" alt="Semnox Logo" class="min-w-[101px]" />
            </a> -->
            <!-- Search Bar -->
            <!-- <div class="hidden lg:flex max-w-52 relative mx-4">
                <lib-text-input placeholder="Search" [icon]="'/assets/icons/magnifier.svg'"
                    [iconAlt]="'Magnifier'"></lib-text-input>
            </div> -->
        </div>

        <div class="flex items-center justify-center space-x-6">
            <nav class="hidden lg:flex items-center justify-between space-x-6">
                @for (item of navItems; track $index) {
                <ng-container>
                    <a [routerLink]="item.link" routerLinkActive="text-secondary-blue font-medium"
                        class="flex items-center gap-2 text-xs xl:text-sm text-primary hover:text-secondary-blue">
                        <img [src]="item.icon" [alt]="item.label" class="w-4 h-4 xl:w-5 xl:h-5" />
                        <span> {{ item.label }}</span>
                    </a>
                </ng-container>
                }

                <!-- My Account -->
                <div class="relative" appClickOutside (clickOutside)="accountDropdown = false">
                    <button class="flex items-center text-xs xl:text-sm text-primary hover:text-secondary-blue"
                        (click)="toggleAccountDropdown()">
                        <img src="/assets/icons/account.svg" alt="Account" class="w-4 h-4 xl:w-5 xl:h-5 mr-2" />
                        <span class="text-nowrap"> My Account</span>
                        <img src="/assets/icons/chevron-down.svg" alt="Dropdown"
                            class="w-4 h-4 xl:w-5 xl:h-5 ml-2 transition-transform" [class.transform]="accountDropdown"
                            [class.rotate-180]="accountDropdown" />
                    </button>

                    <app-dropdown-menu [items]="accountMenuItems" [isOpen]="accountDropdown"
                        (closeMenu)="accountDropdown = false" [alignRight]="true"></app-dropdown-menu>
                </div>
            </nav>

            <div class="flex items-center gap-4">
                <!-- Shopping Cart -->
                <a href="#" class="text-primary">
                    <img src="/assets/icons/cart.svg" alt="Cart" class="min-w-5 min-h-5 xl:w-6 xl:h-6 md:mx-4" />
                </a>

                <!-- Language Selector -->
                <div class="relative" appClickOutside (clickOutside)="languageDropdown = false">
                    <button class="flex items-center justify-center gap-1 px-2 py-1 rounded text-xs xl:text-sm"
                        (click)="toggleLanguageDropdown()">
                        <img src="/assets/icons/language.svg " class="w-5 h-5 xl:w-6 xl:h-6" alt="language" />
                        <span class="min-w-6">
                            {{ selectedLanguage.code.toUpperCase() }}</span>
                        <img src="/assets/icons/chevron-down.svg " alt="Dropdown" class="w-4 h-4 transition-transform"
                            [class.transform]="languageDropdown" [class.rotate-180]="languageDropdown" />
                    </button>

                    <app-dropdown-menu [items]="languageMenuItems" [isOpen]="languageDropdown" [alignRight]="true"
                        [defaultSelection]="languageMenuItems[0]" (closeMenu)="languageDropdown = false"
                        [size]="'sm'"></app-dropdown-menu>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- Mobile Sidebar -->

<app-mobile-sidebar [isOpen]="mobileSidebarOpen" (closeSidebar)="closeSideBar()"></app-mobile-sidebar>