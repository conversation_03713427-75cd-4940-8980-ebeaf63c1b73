/**
 * @fileoverview HeaderComponent extends the BaseComponent and provides specific functionality for the app's header
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-09-18
 */
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { Component, HostListener, Inject, PLATFORM_ID } from '@angular/core';
import { RouterLink, RouterLinkActive } from '@angular/router';
// import { HeaderTheme } from '../../../../../common-config/theme-env';
import { getTemplateUrl, ClientHeaderComponent } from 'lib-app-core';
import { HeaderTheme } from '@theme/waiver/waiver.theme';
import { DropdownMenuComponent } from '../dropdown-menu/dropdown-menu.component';
import { MobileSidebarComponent } from '../mobile-sidebar/mobile-sidebar.component';
import { TextInputComponent } from 'lib-ui-kit';
interface Language {
    code: string;
    name: string;
    icon: string;
}
@Component({
    standalone: true,
    selector: 'app-header',
    imports: [
        RouterLink,
        RouterLinkActive,
        CommonModule,
        DropdownMenuComponent,
        MobileSidebarComponent,
        // TextInputComponent,
        ClientHeaderComponent
    ],
    templateUrl: getTemplateUrl(HeaderTheme, 'header'),
    styleUrls: ['./header.component.scss']
})
export class HeaderComponent {

    isBrowser = false;

    constructor(@Inject(PLATFORM_ID) private platformId: Object) {
        this.isBrowser = isPlatformBrowser(this.platformId);
    }

    // Close Mobile Sidebar
    @HostListener('document:keydown.escape', ['$event'])
    onEscapePress() {
        if (this.isBrowser) {
            this.mobileSidebarOpen = false;
        }
    }

    languages = [
        { code: 'EN', name: 'English', icon: '' },
        { code: 'ES', name: 'Spanish', icon: '' },
        { code: 'FR', name: 'French', icon: '' },
    ];

    navItems = [
        { label: 'Home', icon: '/assets/icons/home.svg', link: '/home' },
        { label: 'Cards', icon: '/assets/icons/card.svg', link: '/my-cards' },
        {
            label: 'Waivers',
            icon: '/assets/icons/preview-blue.svg',
            link: '/waivers',
        },
        {
            label: 'F&B',
            icon: '/assets/icons/food-beverages.svg',
            link: '/f-and-b',
        },
        {
            label: 'Recharge',
            icon: '/assets/icons/recharge.svg',
            link: '/recharge',
        },
        {
            label: 'Reservations',
            icon: '/assets/icons/reservation.svg',
            link: '/reservations',
        },
    ];

    languageMenuItems = this.languages.map(lang => ({
        label: lang.name,
        icon: lang.icon,
        action: () => this.toggleLanguage(lang),
    }));

    selectedLanguage = this.languages[0];
    languageDropdown = false;
    accountDropdown = false;
    mobileSidebarOpen = false;

    accountMenuItems = [
        { label: 'My Profile', link: '/my-accounts/my-profile' },
        { label: 'My Cards', link: '/my-accounts/my-cards' },
        { label: 'My Subscriptions', link: '/my-accounts/my-subscriptions' },
        { label: 'My Signed Waivers', link: '/waivers/my-signed-waivers' },
        { label: 'My Relations', link: '/waivers/my-relations' },
        { label: 'My Orders', link: '/my-accounts/my-orders' },
        { label: 'Change Password', link: '/my-accounts/change-password' },
    ];



    // Toggle Mobile Sidebar
    toggleMobileSidebar(): void {
        this.mobileSidebarOpen = !this.mobileSidebarOpen;

        // Close dropdowns when opening mobile sidebar
        if (this.mobileSidebarOpen) {
            this.languageDropdown = false;
            this.accountDropdown = false;
        }
    }

    // Toggle Language Dropdown
    toggleLanguageDropdown(): void {
        this.languageDropdown = !this.languageDropdown;
        this.accountDropdown = false;
        if (this.mobileSidebarOpen) {
            this.mobileSidebarOpen = false;
        }
    }

    closeSideBar() {
        this.mobileSidebarOpen = false;
    }

    // Toggle Language
    toggleLanguage(language: Language): void {
        this.selectedLanguage = language;
        this.languageDropdown = false;
    }

    // Toggle Account Dropdown
    toggleAccountDropdown(): void {
        this.accountDropdown = !this.accountDropdown;
        this.languageDropdown = false;
    }

    // Close Dropdowns
    closeDropdowns(): void {
        this.languageDropdown = false;
        this.accountDropdown = false;
    }
}
