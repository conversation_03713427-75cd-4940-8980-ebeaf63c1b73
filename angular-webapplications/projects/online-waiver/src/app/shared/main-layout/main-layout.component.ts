import { CommonModule } from '@angular/common';
import { Component, signal } from '@angular/core';
import { ActivatedRoute, RouterOutlet } from '@angular/router';
import { Breadcrumb, BreadCrumbsComponent } from '../bread-crumbs/bread-crumbs.component';
import { Subscription } from 'rxjs';
import { BreadCrumbService } from '../services/bread-crumb.service';

@Component({
    selector: 'app-main-layout',
    imports: [CommonModule, RouterOutlet, BreadCrumbsComponent],
    templateUrl: './main-layout.component.html',
    styleUrl: './main-layout.component.scss'
})
export class MainLayoutComponent {
    readonly breadcrumbs = signal<Breadcrumb[]>([]);
    private breadcrumbSub!: Subscription;

    constructor(
        private breadcrumbService: BreadCrumbService,
        private route: ActivatedRoute
    ) { }

    ngOnInit() {
        this.breadcrumbService.initialize(this.route);
        this.breadcrumbSub = this.breadcrumbService.breadcrumbs$.subscribe(
            (breadcrumbs: Breadcrumb[]) => {
                this.breadcrumbs.set(breadcrumbs);
            }
        );
    }

    ngOnDestroy() {
        if (this.breadcrumbSub) {
            this.breadcrumbSub.unsubscribe(); // Unsubscribe to avoid memory leaks
        }
    }

}
