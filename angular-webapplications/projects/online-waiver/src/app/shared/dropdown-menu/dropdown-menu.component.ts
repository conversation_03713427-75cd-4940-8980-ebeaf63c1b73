import { CommonModule } from '@angular/common';
import { Component, input, output, signal } from '@angular/core';
import { Router, RouterLink, RouterLinkActive } from '@angular/router';
import { ClickOutsideDirective } from 'lib-ui-kit';

export interface DropdownItem {
    label: string;
    link?: string;
    icon?: string;
    action?: () => void;
}

@Component({
    selector: 'app-dropdown-menu',
    imports: [CommonModule, RouterLink, RouterLinkActive],
    templateUrl: './dropdown-menu.component.html',
    styleUrl: './dropdown-menu.component.scss'
})
export class DropdownMenuComponent {
    items = input<DropdownItem[]>([]);
    isOpen = input<boolean>(false);
    alignRight = input<boolean>(false);
    size = input<'sm' | 'md' | 'lg'>('lg');
    defaultSelection = input<DropdownItem | null>(null);
    selectedItem = signal<DropdownItem | null>(null);
    closeMenu = output();

    constructor(private router: Router) { }

    onItemClick(item: DropdownItem): void {
        if (item.action) {
            item.action();
            this.selectedItem.set(item);
        }
        if (item.link) {
            this.router.navigate([item.link]);
            this.selectedItem.set(item);
        }
        this.closeMenu.emit();
    }

    isSelected(item: DropdownItem): boolean {
        if (this.selectedItem()) {
            return this.selectedItem()?.label === item.label;
        } else if (this.defaultSelection()) {
            return this.defaultSelection()?.label === item.label;
        }
        return false;
    }

}
