@if (isOpen()) {
<div class="absolute mt-2 bg-surface-white min-w-max rounded-3xl border border-surface z-20 overflow-hidden"
    [class.right-0]="alignRight()" tabindex="0" [ngClass]="{
        'p-2 md:p-3 md:pb-1': size() === 'sm',
        'p-3 md:p-4 md:pb-3': size() === 'md',
        'p-5 md:p-6 md:pb-4': size() === 'lg',
      }">
    <div>
        @for (item of items(); track $index) {
        <ng-container>
            @if (item.link) {
            <a [routerLink]="item.link" [routerLinkActive]="[
                  'bg-surface-lightest',
                  'text-secondary-blue',
                ]"
                class="flex items-center min-w-32 text-left p-1.5 mb-2 text-sm text-primary rounded-lg hover:bg-surface-lightest"
                (click)="onItemClick(item)" (touchstart.passive)="onItemClick(item)">
                @if (item.icon) {
                <img [src]="item.icon" class="w-5 h-5 mr-3" [alt]="item.label" />
                }
                {{ item.label }}
            </a>
            }
            @if (!item.link) {
            <button type="button"
                class="flex items-center min-w-32 text-left p-1.5 mb-2 text-sm text-primary rounded-lg hover:bg-surface-lightest"
                [ngClass]="{
                  'bg-surface-lightest text-secondary-blue': isSelected(item),
                }" (click)="onItemClick(item)" (touchstart.passive)="onItemClick(item)">
                @if (item.icon) {
                <img [src]="item.icon" class="w-5 h-5 mr-3" [alt]="item.label" />
                }
                {{ item.label }}
            </button>
            }
        </ng-container>
        }
    </div>
</div>
}