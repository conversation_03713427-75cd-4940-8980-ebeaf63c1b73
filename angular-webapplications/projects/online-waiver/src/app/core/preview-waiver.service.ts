/**

* @fileoverview 
* @file preview-waiver.service.ts
* @description Service used by preview waiver component, that holds generic functions and calls remote API's for Post/Get Requests 
* <AUTHOR> B
* @date March 3, 2025
 */

import { HttpClient, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { DEFAULT_APP_CONFIG_TOKEN, EnvService, getConstantValue } from 'lib-app-core';
import { WaiverSetContainerResponse } from '../models/waiver_set_container.model';

@Injectable({
    providedIn: 'root',
})
export abstract class PreviewWaiverService {


private envService = inject(EnvService);
    private defaultAppConfig = inject(DEFAULT_APP_CONFIG_TOKEN);
    
    protected parafaitApiBaseUrl: string = this.envService.parafaitApiBaseUrl;
    // protected siteId: number = this.defaultAppConfig['siteId']; // Default site identifier
    protected siteId: number = 1010; // Default site identifier
    private waiverAPIEndPoints: any;  

    constructor(private http: HttpClient) {
        // Assigning the values of API End points 
        this.waiverAPIEndPoints = getConstantValue('API_ENDPOINTS');
    }

    //Gets the waiver set container response of current site
    getWaiverSetContainer() {
        return this.http.get<WaiverSetContainerResponse>(`${this.parafaitApiBaseUrl + this.waiverAPIEndPoints.GET_WAIVER_SET_CONTAINER}?siteId=${this.siteId}`);
    }

    /* validates HTML waivers 
     Customer can use  these to check how the waiver is looking without actually saving or overwriting the waiver , it uses the defualt customer*/
    validateHtmlWaiver() {
        return this.http.get(`${this.parafaitApiBaseUrl + this.waiverAPIEndPoints.VALIDATE_HTML_WAIVER}`);
    }


    createCustomerWaiver(selectedWaiverSetId: number, validatedHtmlWaiverResp: any) {
        //constructing the options required
        const custId = validatedHtmlWaiverResp[0].Id;
        const createCustomerWaiverPayload = {
            Channel: "WEBSITE",
            SignForCustomersIdList: validatedHtmlWaiverResp.map((custData: any) => custData.Id),
            SignatoryGuestCustomerDTO: {},
            SignForGuestCustomersDTOList: validatedHtmlWaiverResp
        };


        // setting the params
        let params = new HttpParams()
            .set('postMode', 'false')
            .set('previewMode', 'true')

        
        return this.http.post(`${this.parafaitApiBaseUrl + this.waiverAPIEndPoints.CREATE_CUSTOMER_WAIVER.replace('{custId}', custId).replace('{selectedWaiverSetId}', selectedWaiverSetId)}`, createCustomerWaiverPayload, { params });
    }

    //Converts base64 into UTF8 format
    base64ToUtf8(base64Str: string): string {
        const binaryString = atob(base64Str);
        let utf8String = '';
        Array.from(binaryString).forEach((character, index) => {
            utf8String += String.fromCharCode(binaryString.charCodeAt(index))
        })
        return decodeURIComponent(escape(utf8String));
    }

    
}
