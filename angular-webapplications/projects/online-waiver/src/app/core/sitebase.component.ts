/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-09-18
 */

// BaseComponent provides common logic for all derived components, such as style handling and menu initialization
import { Directive, OnInit, OnD<PERSON>roy, Injector } from '@angular/core';
import { BaseComponent } from 'lib-app-core';
import { SiteWaiverTheme } from '@theme/waiver/waiver.theme';
// import { SiteWaiverTheme } from '../../../../common-config/theme-env';

@Directive() // This is a base class for components to extend
export abstract class SiteBaseComponent extends BaseComponent {
	
    protected getSiteTheme(){
        return SiteWaiverTheme;
    }

}

