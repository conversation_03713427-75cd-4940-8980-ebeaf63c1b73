/**
 * @fileoverview
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-03-12
 */

import { Injectable, inject } from '@angular/core';
import {
    AuthenticateSystemUsersDTOModel,
    DataTransferService,
    ExecutionContextBaseInterceptor,
} from 'lib-app-core';
import { AppInitService } from './app-init.service';
import { waiverConstants } from '../constants/waiver.constant';

@Injectable()
export class ExecutionContextInterceptor extends ExecutionContextBaseInterceptor {
    private appInitService = inject(AppInitService);
    private dataTransferService = inject(DataTransferService);
    // authenticateSystemUsersService = inject(AuthenticateSystemUsersService)

    protected getWebApiToken(): string | null {
        // return this.authenticateSystemUsersService.getWebApiToken();
        const data =
            this.dataTransferService.getData<AuthenticateSystemUsersDTOM<PERSON><PERSON> | null>(
                waiverConstants.TRANSFER_STATE_KEYS
                    .AUTHENTICATE_SYSTEM_USERS_DATA,
                null
            );

        return data?.WebApiToken ?? this.appInitService.webApiToken$;
    }
}
