/* You can add global styles to this file, and also import other style files */
@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
    font-family: 'Onest';
    src: url('/assets/fonts/Onest-VariableFont.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Orbitron';
    src: url('/assets/fonts/Orbitron-VariableFont.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

input:focus {
    outline: none;
}

@layer base {
    @media (min-width: 1440px) {
        html {
            /* Reserve space for the scrollbar, preventing layout shift */
            scrollbar-gutter: stable;
        }
    }

    body {
        @apply bg-surface-lighter text-primary;
        font-family:
            'Onest',
            'Inter',
            -apple-system,
            BlinkMacSystemFont,
            'Segoe UI',
            Roboto,
            Oxygen,
            Ubuntu,
            Cantarell,
            'Open Sans',
            'Helvetica Neue',
            sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* Custom Scrollbar Styles */
    /* For Webkit browsers (Chrome, Safari, newer versions of Opera) */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        background: transparent;
    }

    ::-webkit-scrollbar-thumb {
        @apply bg-scrollbar-thumb;
        border-radius: 10px;
        min-height: 60px;
    }

    ::-webkit-scrollbar-thumb:hover {
        @apply bg-scrollbar-thumb;
    }

    /* For Firefox */
    * {
        scrollbar-width: thin;
        scrollbar-color: #c8d2e680 transparent;
    }
}

/* we apply this class to body to ensure it does not scroll when modal is displayed*/
.modal-open {
    height: 100vh;
    touch-action: none;
    -webkit-overflow-scrolling: none;
    overflow: hidden;
    overscroll-behavior: none;
}

/* need to set same background as that of modal overlay when modal is shown so that gutter area
does not reveal the actual background color of html */
html:has(body.modal-open) {
    @apply bg-modal-overlay transition-colors duration-200 ease-in-out;
}