/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-03-06
 */

import { BaseServer } from '../../../lib/lib-app-core/src/lib/server/base-server';
import bootstrap from './main.server';

class WaiverServer extends BaseServer {
	constructor() {
		super('online-waiver', bootstrap);
	}

	protected override configureServer(): void {
		super.configureServer();

		// Register API Route
		this.app.get('/api/waiver-info', (req, res) => {
			res.json({ waiver: 'This is waiver-specific data.' });
		});

		console.log('Waiver-specific API routes registered.');
	}
}

// Start the server
const port = process.env['PORT'] ? parseInt(process.env['PORT'], 10) : 4200;
new WaiverServer().start(port);



