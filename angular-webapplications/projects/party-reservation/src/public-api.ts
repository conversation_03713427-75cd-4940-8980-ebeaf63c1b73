/**
 * @fileoverview Public API for Party Reservation Project
 * 
 * This file exports all the components, services, and other public APIs
 * that should be available when importing from the party-reservation project.
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-28
 */

// Export main components
export * from './app/components/party-reservation-main/party-reservation-main.component';
export * from './app/components/reservation-booking-form/reservation-booking-form.component';

// Export routes for external use
export * from './app/app.routes';

// Export any shared interfaces or types (if needed in the future)
// export * from './app/interfaces/reservation.interface';
// export * from './app/services/reservation.service';
