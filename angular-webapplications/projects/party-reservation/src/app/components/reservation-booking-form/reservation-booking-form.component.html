<div class="max-w-4xl mx-auto p-6">
    <div class="bg-surface-white rounded-4xl p-8 shadow-md">
        <h2 class="text-2xl font-bold text-primary mb-6">Book Your Reservation</h2>
        
        <form [formGroup]="bookingForm" (ngSubmit)="onSubmit()" class="space-y-6">
            <!-- Customer Information Section -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="customerName" class="block text-sm font-medium text-gray-700 mb-2">
                        Full Name *
                    </label>
                    <input
                        type="text"
                        id="customerName"
                        formControlName="customerName"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        [class.border-red-500]="isFieldInvalid('customerName')"
                        placeholder="Enter your full name">
                    @if (isFieldInvalid('customerName')) {
                        <p class="text-red-500 text-sm mt-1">{{ getFieldError('customerName') }}</p>
                    }
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        Email Address *
                    </label>
                    <input
                        type="email"
                        id="email"
                        formControlName="email"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        [class.border-red-500]="isFieldInvalid('email')"
                        placeholder="Enter your email">
                    @if (isFieldInvalid('email')) {
                        <p class="text-red-500 text-sm mt-1">{{ getFieldError('email') }}</p>
                    }
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number *
                    </label>
                    <input
                        type="tel"
                        id="phone"
                        formControlName="phone"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        [class.border-red-500]="isFieldInvalid('phone')"
                        placeholder="1234567890">
                    @if (isFieldInvalid('phone')) {
                        <p class="text-red-500 text-sm mt-1">{{ getFieldError('phone') }}</p>
                    }
                </div>

                <div>
                    <label for="guestCount" class="block text-sm font-medium text-gray-700 mb-2">
                        Number of Guests *
                    </label>
                    <input
                        type="number"
                        id="guestCount"
                        formControlName="guestCount"
                        min="1"
                        max="100"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        [class.border-red-500]="isFieldInvalid('guestCount')"
                        placeholder="Enter number of guests">
                    @if (isFieldInvalid('guestCount')) {
                        <p class="text-red-500 text-sm mt-1">{{ getFieldError('guestCount') }}</p>
                    }
                </div>
            </div>

            <!-- Event Details Section -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="eventDate" class="block text-sm font-medium text-gray-700 mb-2">
                        Event Date *
                    </label>
                    <input
                        type="date"
                        id="eventDate"
                        formControlName="eventDate"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        [class.border-red-500]="isFieldInvalid('eventDate')">
                    @if (isFieldInvalid('eventDate')) {
                        <p class="text-red-500 text-sm mt-1">{{ getFieldError('eventDate') }}</p>
                    }
                </div>

                <div>
                    <label for="eventTime" class="block text-sm font-medium text-gray-700 mb-2">
                        Event Time *
                    </label>
                    <select
                        id="eventTime"
                        formControlName="eventTime"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        [class.border-red-500]="isFieldInvalid('eventTime')">
                        <option value="">Select time</option>
                        @for (time of timeSlots; track time) {
                            <option [value]="time">{{ time }}</option>
                        }
                    </select>
                    @if (isFieldInvalid('eventTime')) {
                        <p class="text-red-500 text-sm mt-1">{{ getFieldError('eventTime') }}</p>
                    }
                </div>

                <div>
                    <label for="packageType" class="block text-sm font-medium text-gray-700 mb-2">
                        Package Type *
                    </label>
                    <select
                        id="packageType"
                        formControlName="packageType"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        [class.border-red-500]="isFieldInvalid('packageType')">
                        <option value="">Select package</option>
                        @for (package of packageTypes; track package.value) {
                            <option [value]="package.value">{{ package.label }}</option>
                        }
                    </select>
                    @if (isFieldInvalid('packageType')) {
                        <p class="text-red-500 text-sm mt-1">{{ getFieldError('packageType') }}</p>
                    }
                </div>
            </div>

            <!-- Special Requests -->
            <div>
                <label for="specialRequests" class="block text-sm font-medium text-gray-700 mb-2">
                    Special Requests
                </label>
                <textarea
                    id="specialRequests"
                    formControlName="specialRequests"
                    rows="4"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="Any special requests or dietary restrictions..."></textarea>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end">
                <button
                    type="submit"
                    [disabled]="isSubmitting"
                    class="bg-primary text-white px-8 py-3 rounded-lg font-medium hover:bg-primary-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
                    @if (isSubmitting) {
                        <span class="flex items-center">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Submitting...
                        </span>
                    } @else {
                        Submit Reservation
                    }
                </button>
            </div>
        </form>
    </div>
</div>
