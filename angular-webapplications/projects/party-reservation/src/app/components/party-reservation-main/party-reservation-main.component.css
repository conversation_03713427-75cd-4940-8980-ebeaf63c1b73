/* Custom styles for party reservation main component */

.reservation-card {
    transition: transform 0.2s ease-in-out;
}

.reservation-card:hover {
    transform: translateY(-2px);
}

.feature-icon {
    width: 24px;
    height: 24px;
    color: var(--color-primary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .reservation-grid {
        grid-template-columns: 1fr;
    }
}

/* Animation for cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.reservation-card {
    animation: fadeInUp 0.6s ease-out;
}

/* Button hover effects */
.select-button {
    position: relative;
    overflow: hidden;
}

.select-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.select-button:hover::before {
    left: 100%;
}
