<div class="max-w-7xl mx-auto p-6">
    <!-- Header Section -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-primary mb-2">{{ title }}</h1>
        <p class="text-gray-600">Choose the perfect party package for your special event</p>
    </div>

    <!-- Reservation Types Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        @for (type of reservationTypes; track type.id) {
            <div class="bg-surface-white rounded-4xl p-6 shadow-md hover:shadow-lg transition-shadow duration-300 border border-gray-100">
                <div class="mb-4">
                    <h3 class="text-xl font-semibold text-primary mb-2">{{ type.name }}</h3>
                    <p class="text-gray-600 text-sm mb-4">{{ type.description }}</p>
                </div>
                
                <div class="space-y-2 mb-6">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Price:</span>
                        <span class="font-semibold text-primary">${{ type.price }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Duration:</span>
                        <span class="text-sm">{{ type.duration }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Max Guests:</span>
                        <span class="text-sm">{{ type.maxGuests }}</span>
                    </div>
                </div>
                
                <button 
                    (click)="onSelectReservationType(type)"
                    class="w-full bg-primary text-white py-3 px-4 rounded-lg font-medium hover:bg-primary-800 transition-colors duration-200">
                    Select Package
                </button>
            </div>
        }
    </div>

    <!-- Additional Information Section -->
    <div class="bg-surface-lighter rounded-4xl p-6">
        <h2 class="text-xl font-semibold text-primary mb-4">What's Included</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-primary rounded-full mt-2"></div>
                <div>
                    <h4 class="font-medium text-primary">Dedicated Party Area</h4>
                    <p class="text-sm text-gray-600">Private space for your celebration</p>
                </div>
            </div>
            <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-primary rounded-full mt-2"></div>
                <div>
                    <h4 class="font-medium text-primary">Party Host</h4>
                    <p class="text-sm text-gray-600">Dedicated staff member to assist</p>
                </div>
            </div>
            <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-primary rounded-full mt-2"></div>
                <div>
                    <h4 class="font-medium text-primary">Setup & Cleanup</h4>
                    <p class="text-sm text-gray-600">We handle all the details</p>
                </div>
            </div>
            <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-primary rounded-full mt-2"></div>
                <div>
                    <h4 class="font-medium text-primary">Basic Decorations</h4>
                    <p class="text-sm text-gray-600">Balloons and table settings included</p>
                </div>
            </div>
        </div>
    </div>
</div>
