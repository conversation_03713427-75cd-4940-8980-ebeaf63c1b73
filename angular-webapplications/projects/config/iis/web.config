<configuration>
    <system.webServer>
        <handlers>
            <add name="SSR" path="*" verb="*" modules="httpPlatformHandler"
                resourceType="Unspecified" />
        </handlers>
        <httpPlatform processPath="C:\Program Files\nodejs\node.exe"
            arguments="server\server.mjs"
            stdoutLogEnabled="true"
            stdoutLogFile=".\node.log">
            <environmentVariables>
                <environmentVariable name="PORT" value="%HTTP_PLATFORM_PORT%" />
                <environmentVariable name="NODE_ENV" value="Production" />
            </environmentVariables>
        </httpPlatform>
    </system.webServer>
</configuration>