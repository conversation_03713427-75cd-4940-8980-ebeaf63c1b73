import { Config } from 'tailwindcss';
import { CustomerTailwindConfig } from './theme/common/customer.tailwind.config';

const config: Config = {
    content: ['./src/**/*.{html,ts}', './lib/**/*.{html,ts}', './projects/**/*.{html,ts}'],
    theme: {
        // container: CustomerTailwindConfig.container,
		// screens: CustomerTailwindConfig.screens,
        // fontFamily: CustomerTailwindConfig.fontFamily,
		// extend: CustomerTailwindConfig.extend,
		// colors: CustomerTailwindConfig.colors,
        extend: CustomerTailwindConfig.extend
    },

    plugins: [],
};

export default config;
